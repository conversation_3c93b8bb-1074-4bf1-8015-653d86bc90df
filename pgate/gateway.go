package pgate

import (
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/armon/go-socks5"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/google/go-cmp/cmp"
	"github.com/jinzhu/copier"
	"github.com/p4gefau1t/trojan-go/common"
	"github.com/p4gefau1t/trojan-go/log"
	"github.com/spf13/cast"
	"golang.org/x/exp/slices"
	goproxy "golang.org/x/net/proxy"

	_ "github.com/p4gefau1t/trojan-go/log/golog"
	tproxy "github.com/p4gefau1t/trojan-go/proxy"
	_ "github.com/p4gefau1t/trojan-go/proxy/client"
	_ "github.com/p4gefau1t/trojan-go/tunnel/router"
	"github.com/wizhodl/quanter/common/zlog"
)

type ForwarderStatus string

const ForwarderStatusNotStarted = ""
const ForwarderStatusOK = "ok"
const ForwarderStatusError = "error"

func failJSON(c *gin.Context, format string, args ...any) {
	code := http.StatusInternalServerError
	c.AbortWithStatusJSON(code, gin.H{"ok": false, "message": fmt.Sprintf(format, args...)})
}

func okJSON(ctx *gin.Context, v any) {
	if v == nil {
		ctx.JSON(http.StatusOK, gin.H{"ok": true, "data": ""})
	} else if s, ok := v.(string); ok {
		ctx.JSON(http.StatusOK, gin.H{"ok": true, "data": s})
	} else {
		ctx.JSON(http.StatusOK, gin.H{"ok": true, "data": v})
	}
}

func okString(ctx *gin.Context, v string) {
	ctx.String(http.StatusOK, v)
}

func badRequest(ctx *gin.Context, format string, args ...any) {
	code := http.StatusBadRequest
	ctx.AbortWithStatusJSON(code, gin.H{"ok": false, "message": fmt.Sprintf(format, args...)})
}

// Forwarder 代理的转发器
// 代理的转发器是一个 trojan-go 的客户端，用于转发到代理的流量
type Forwarder struct {
	client       *tproxy.Proxy  `json:"-"`
	socks5Server *socks5.Server `json:"-"`

	Status    ForwarderStatus `json:"status"`
	Proxy     string          `json:"proxy"`
	Port      int             `json:"port"` // 转发器端口，为了保持 forwarder 的端口的稳定性，不要随意变更，会写到持久化里面
	StartTime time.Time       `json:"start_time"`

	// Unified proxy support - same port for HTTP/HTTPS/SOCKS5
	unifiedServer *http.Server `json:"-"` // Unified HTTP/HTTPS/SOCKS5 server
}

// Close closes the forwarder and cleans up resources
func (f *Forwarder) Close() error {
	if f.client != nil {
		// Trojan client cleanup if needed
		f.client = nil
	}
	if f.socks5Server != nil {
		// SOCKS5 server cleanup if needed
		f.socks5Server = nil
	}
	if f.unifiedServer != nil {
		// Shutdown unified server
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		f.unifiedServer.Shutdown(ctx)
		f.unifiedServer = nil
	}
	f.Status = ForwarderStatusNotStarted
	return nil
}

// IsReady returns true if the forwarder is ready to accept connections
func (f *Forwarder) IsReady() bool {
	return f != nil && f.Status == ForwarderStatusOK && f.Port > 0
}

// GetProxyURL returns the proxy URL for this forwarder
func (f *Forwarder) GetProxyURL() string {
	if !f.IsReady() {
		return ""
	}
	return fmt.Sprintf("socks5://127.0.0.1:%d", f.Port)
}

// GetHTTPProxyURL returns the HTTP proxy URL for this forwarder
func (f *Forwarder) GetHTTPProxyURL() string {
	if !f.IsReady() {
		return ""
	}
	return fmt.Sprintf("http://127.0.0.1:%d", f.Port)
}

// GetShellEnv returns the shell environment variables for this forwarder
func (f *Forwarder) GetShellEnv() string {
	if !f.IsReady() {
		return "[ERROR] forwarder is not ready"
	}
	proxyURL := f.GetHTTPProxyURL()
	return fmt.Sprintf("export HTTP_PROXY=%s; export HTTPS_PROXY=%s; export ALL_PROXY=%s", proxyURL, proxyURL, proxyURL)
}

// GetUptime returns the uptime duration of this forwarder
func (f *Forwarder) GetUptime() time.Duration {
	if f.StartTime.IsZero() {
		return 0
	}
	return time.Since(f.StartTime)
}

// CheckClientReady checks if the underlying client (SOCKS5 or Trojan) is ready
func (f *Forwarder) CheckClientReady() bool {
	if f == nil {
		return false
	}

	// Check if either SOCKS5 or Trojan client is initialized
	if f.socks5Server != nil {
		return true
	}
	if f.client != nil {
		return true
	}
	if f.unifiedServer != nil {
		return true
	}

	return false
}

type LoginAccount struct {
	UID          string     `json:"uid"`
	Project      string     `json:"project"` // 唯一性检查是根据 project 来判断的
	LastActive   *time.Time `json:"last_active"`
	IPAddr       string     `json:"ip_addr"`
	ScriptIPAddr string     `json:"script_ip_addr"` // 脚本对应的 ip 地址，不是登录的 ip 地址，如果不为空，放到 surge 配置文件中

	Forwarder *Forwarder `json:"-"` // 从 LoginAccounts 中拷贝过来，可能为 nil
	Proxy     *Proxy     `json:"-"` // 从 LoginAccounts 中拷贝过来，方便用
}

type ProxyLoginBook struct {
	ProxyName string          `json:"proxy_name"`
	Accounts  []*LoginAccount `json:"accounts"`

	Proxy     *Proxy     `json:"proxy"`
	Forwarder *Forwarder `json:"forwarder"` // 更新 proxy 的时候就准备好 forwarder，account 是否有 forwarder 取决于是否 enable_forwarder
}

// 如果 project 以 _ 开头，表示启用 surge ip rules，即按照 ip 来判断是否使用代理
// pchrome 的项目都不会启用 surge ip rules，否则会导致 proxy 乱掉
func isSurgeIPRulesEnabled(project string) bool {
	return strings.HasPrefix(project, "_")
}

type ProxyGateway struct {
	manager *ProxyManager
	mutex   sync.Mutex
}

type GroupForwarder struct {
	Group     string          `json:"group"`
	Status    ForwarderStatus `json:"status"`
	Port      int             `json:"port"`
	StartTime time.Time       `json:"start_time"`
	Proxies   []string        `json:"proxies"`

	gateway  *ProxyGateway `json:"-"`
	listener *net.Listener `json:"-"`
	mutex    sync.Mutex    `json:"-"`
}

// NewProxyGateway 创建一个代理网关
// proxyLogins 参数是从 storage 里面读取的，如果没有就传 nil
func NewProxyGateway(manager *ProxyManager) *ProxyGateway {
	SetDebugMode(manager.Options.Debug)

	gateway := &ProxyGateway{
		manager: manager,
	}
	// 每次启动清空 LastActive ，只有登录后才赋值
	for _, loginBook := range gateway.manager.ProxyLogins {
		for _, account := range loginBook.Accounts {
			account.LastActive = nil
		}
		loginBook.Forwarder.Status = ForwarderStatusNotStarted
	}
	go gateway.RefreshProxies()
	gateway.SetupHandlers()
	return gateway
}

func SetDebugMode(debug bool) {
	if debug {
		gin.SetMode(gin.DebugMode)
		gin.DefaultWriter = os.Stdout
		log.SetLogLevel(log.AllLevel)
	} else {
		gin.SetMode(gin.ReleaseMode)
		gin.DefaultWriter = io.Discard
		log.SetLogLevel(log.OffLevel)
	}
}

func (this *ProxyGateway) StartServer(address string, wg *sync.WaitGroup) {
	defer wg.Done()

	router := this.SetupHandlers()

	// Log the error if the server fails to start
	if err := router.Run(address); err != nil {
		zlog.Panicf("Failed to run server on %s: %v", address, err)
	} else {
		zlog.Infof("Server started on %s", address)
	}
}

func (this *ProxyGateway) SetupHandlers() *gin.Engine {
	r := gin.Default()

	pprofGroup := r.Group("/debug/pprof")
	pprof.RouteRegister(pprofGroup)

	r.GET("/", this.IndexHandler)
	r.GET("/ping", this.PingHandler)
	r.GET("/login", this.LoginHandler)
	r.GET("/login/script", this.ScriptLoginHandler)
	r.GET("/logout", this.LogoutHandler)
	r.GET("/logout/script", this.ScriptLogoutHandler)
	r.GET("/surge/config", this.SurgeConfigHandler)
	r.GET("/forwarders", this.ListAccountForwardersHandler)
	r.GET("/forwarder", this.GetForwarderHandler)
	r.GET("/group/forwarders", this.ListGroupForwardersHandler)
	r.GET("/proxies", this.ListProxiesHandler)
	r.GET("/proxies/sync/check", this.CheckSyncProxiesHandler)
	r.GET("/proxies/sync", this.SyncProxiesHandler)
	r.GET("/proxy/test", this.TestProxiesHandler)
	r.GET("/proxy/logins", this.ListProxyLoginsHandler)
	r.GET("/proxy/templates", this.ListProxyTemplates)
	r.GET("/proxy/name/suggest", this.SuggestNameHandler)
	r.GET("/proxy/name/check", this.CheckNameHandler)
	r.GET("/proxy/create", this.CreateProxyHandler)
	r.GET("/proxy/create/socks5", this.CreateSocks5ProxyHandler)
	r.GET("/proxy/delete", this.DeleteProxyHandler)
	r.GET("/proxy/repair", this.RepairProxyHandler)
	r.GET("/proxy/stop", this.StopProxyHandler)
	r.GET("/proxy/start", this.StartProxyHandler)
	r.GET("/proxy/remove/log", this.RemoveLogForProgramHandler)
	r.GET("/proxy/clear/accounts", this.ClearAccountsForProxyHandler)
	r.GET("/proxy/set/account/limit", this.SetAccountLimitHandler)
	r.GET("/proxy/account/limits", this.ListAccountLimitsHandler)

	r.GET("/proxy/extend", this.IncreaseLifeHourHandler)
	r.GET("/aws/reserved/instances", this.ListAWSReservedInstancesHandler)
	r.GET("/aws/reservation/coverage", this.ListAWSReservationCoverageHandler)
	r.GET("/google/commitments", this.ListGoogleCommitmentsHandler)
	r.GET("/google/commitments/coverage", this.ListGoogleCommitmentsCoverageHandler)

	r.Static("/pchrome/files", filepath.Join(workingDir(), "pchrome"))
	r.GET("/pchrome/version", this.PchromeBinaryVersionHandler)

	pprof.Register(r)
	return r
}

func workingDir() string {
	ex, err := os.Executable()
	if err != nil {
		panic(err)
	}
	exPath := filepath.Dir(ex)
	return exPath
}

func (this *ProxyGateway) PchromeBinaryVersionHandler(c *gin.Context) {
	platform := c.Query("platform")
	if platform == "" {
		platform = "win_arm64"
	}

	binaryPrefix := ""
	if platform == "win_arm64" {
		binaryPrefix = "pchrome_arm64"
	} else if platform == "macos" {
		binaryPrefix = "pchrome_macos"
	}

	// get all binary in workingDir
	files, err := os.ReadDir(filepath.Join(workingDir(), "pchrome"))
	if err != nil {
		zlog.Errorf("os.ReadDir => %v", err.Error())
		failJSON(c, "list working dir failed, %v", err.Error())
		return
	}
	// find the latest binary
	latestVersionTime := time.Time{}
	latestVersionFile := ""
	for _, f := range files {
		fName := strings.Split(f.Name(), ".")[0]
		parts := strings.Split(fName, "_")

		if !strings.HasPrefix(fName, binaryPrefix) && len(parts) != 3 {
			continue
		}
		buildTimeStr := parts[2]
		loc := time.FixedZone("CST", 8*60*60) // CST is China Standard Time, which is UTC+8
		buildTime, err := time.ParseInLocation("20060102150405", buildTimeStr, loc)
		if err != nil {
			zlog.Errorf("parse build time failed: %v", err)
			continue
		}
		if buildTime.After(latestVersionTime) {
			latestVersionTime = buildTime
			latestVersionFile = f.Name()
		}
	}
	if latestVersionFile != "" {
		okJSON(c, gin.H{"build_time": latestVersionTime.Unix(), "file": latestVersionFile})
		return
	}
	failJSON(c, "binaries not found")
}

func (this *ProxyGateway) RefreshProxies() {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	proxies := this.manager.GetProxies()

	// 删除已经删除的 proxy 的登录记录
	for _, proxy := range proxies {
		if this.checkProxyDeleted(proxy) {
			delete(this.manager.ProxyLogins, proxy.Name)
		}
	}
	for name := range this.manager.ProxyLogins {
		if p := this.manager.GetProxyByName(name); p == nil {
			delete(this.manager.ProxyLogins, name)
		}
	}

	var groups []string
	for _, proxy := range proxies {
		// 如果 proxy 不可用，那么就不用创建 forwarder
		// 但是不删除映射关系
		if !this.checkProxyOK(proxy) {
			continue
		}

		if proxy.template == nil { // 有可能同步等情况下，template 没有设置
			proxy.template = this.manager.Options.GetTemplate(proxy.TemplateName)
		}

		var loginBook *ProxyLoginBook
		// 如果 proxy 的用户登录记录不存在，那么就创建一个
		if _, ok := this.manager.ProxyLogins[proxy.Name]; !ok {
			loginBook = &ProxyLoginBook{
				ProxyName: proxy.Name,
				Accounts:  []*LoginAccount{},
				Proxy:     proxy,
			}
			this.manager.ProxyLogins[proxy.Name] = loginBook
		} else {
			loginBook = this.manager.ProxyLogins[proxy.Name]
			loginBook.Proxy = proxy
		}
		// 默认给每个 proxy 创建一个 Forwarder，为 account 设置 forwarder
		// 如果是从配置文件中加载的 Forwarder，也一样需要初始化其中的 client
		if loginBook.Forwarder == nil || !loginBook.Forwarder.CheckClientReady() {
			forwarder, err := this.SetupForwarderForProxy(proxy, loginBook.Forwarder)
			if err != nil {
				zlog.Errorf("创建 Forwarder 失败: %v, proxy: %s", err, proxy.Name)
			} else {
				zlog.Infof("创建 Forwarder 成功，proxy: %s", proxy.Name)
				loginBook.Forwarder = forwarder
			}
		}
		// 自动设置好登录账号，暂时不要启用，需要用 pchrome 登录
		// for _, account := range loginBook.Accounts {
		// 	this.setupForwarderForAccount(account)
		// }

		if proxy.Group != "" && !slices.Contains(groups, proxy.Group) {
			groups = append(groups, proxy.Group)
		}
	}

	// 设置 group forwarder
	for _, group := range groups {
		var forwarder *GroupForwarder
		if _, ok := this.manager.GroupForwarders[group]; !ok {
			forwarder = &GroupForwarder{
				Group: group,
			}
			this.manager.GroupForwarders[group] = forwarder
		} else {
			forwarder = this.manager.GroupForwarders[group]
		}
		// 如果 group forwarder 是从 storage 读取到的，或者新建的
		// 尝试设置 gateway 和 mutex
		if forwarder.gateway == nil {
			forwarder.gateway = this
			forwarder.mutex = sync.Mutex{}
		}

		err := this.SetupGroupForwarder(forwarder)
		if err != nil {
			zlog.Errorf("setup group forwarder failed: %v", err)
		} else {
			zlog.Infof("setup group forwarder success, group: %s", group)
		}
	}

	// 保存到持久化
	this.save()
}

func (this *ProxyGateway) SetupGroupForwarder(forwarder *GroupForwarder) error {
	// 只允许设置一次，不要重复调用
	if forwarder.listener != nil {
		zlog.Infof("group forwarder already running on %s, group: %s", (*forwarder.listener).Addr().String(), forwarder.Group)
		return nil
	}
	if forwarder.Port == 0 {
		port := 0
		// pick a port for forwarder
		for i := 0; i < 3; i++ {
			port = common.PickPort("tcp", "127.0.0.1")
			existingPorts := this.GetForwarderPorts()
			if slices.Contains(existingPorts, fmt.Sprintf("%d", port)) {
				zlog.Warnf("port %d already in use, retrying...", port)
				time.Sleep(1 * time.Second)
			} else {
				break
			}
		}
		if port == 0 {
			return fmt.Errorf("pick port failed")
		}

		forwarder.Port = port
	}
	forwarder.UpdateProxies()
	go forwarder.Run()
	return nil
}

func (this *ProxyGateway) GetGroupForwarder(group string) *GroupForwarder {
	if forwarder, ok := this.manager.GroupForwarders[group]; ok {
		return forwarder
	}
	return nil
}

// ParseLocalProxy 解析 pgate 本地代理
// url: "pgate://group/tk"
// url: "pgate://proxy/time"
// url: "pgate://template/xxx", 未实现
// 兼容： "<pgate://group/tk>" 的格式
func (this *ProxyGateway) ParseLocalProxy(url string) (realProxy string, er error) {
	protocol := "pgate://"
	if strings.HasPrefix(url, protocol) {
		path := strings.TrimPrefix(url, protocol)
		if strings.HasPrefix(path, "group/") {
			group := strings.TrimPrefix(path, "group/")
			forwarder := this.GetGroupForwarder(group)
			// 函数调用方会检查是否为 "group forwarder not ready" 错误，决定是否重试
			if forwarder == nil {
				return "", fmt.Errorf("group forwarder not ready, forwarder not found, group: %s", group)
			}
			forwarder.UpdateProxies()
			if forwarder.Status != ForwarderStatusOK {
				return "", fmt.Errorf("group forwarder not ready, forwarder status is not ok, group: %s, status: %s", group, forwarder.Status)
			}
			return fmt.Sprintf("socks5://127.0.0.1:%d", forwarder.Port), nil
		} else if strings.HasPrefix(path, "proxy/") {
			proxyName := strings.TrimPrefix(path, "proxy/")
			proxyLogin, ok := this.manager.ProxyLogins[proxyName]
			if !ok {
				return "", fmt.Errorf("proxy not found, proxy: %s", proxyName)
			}
			forwarder := proxyLogin.Forwarder
			if forwarder == nil {
				return "", fmt.Errorf("forwarder not inited, proxy: %s", proxyName)
			}
			return fmt.Sprintf("socks5://127.0.0.1:%d", forwarder.Port), nil
		}
	}
	return "", fmt.Errorf("invalid url: %s", url)
}

func (this *GroupForwarder) Run() {
	if !this.mutex.TryLock() {
		zlog.Infof("group forwarder already running on %s, group: %s", (*this.listener).Addr().String(), this.Group)
		return
	}
	defer this.mutex.Unlock()

	listener, err := net.Listen("tcp", fmt.Sprintf("127.0.0.1:%d", this.Port))
	if err != nil {
		zlog.Errorf("Failed to listen on %s: %v", this.Port, err)
		this.Status = ForwarderStatusError
		return
	}
	defer listener.Close()

	this.listener = &listener
	this.StartTime = time.Now()
	this.Status = ForwarderStatusOK

	zlog.Infof("Group forwarder running on %s, group: %s", listener.Addr().String(), this.Group)

	for {
		clientConn, err := listener.Accept()
		if err != nil {
			zlog.Errorf("Failed to accept connection: %v", err)
			continue
		}
		go this.handleGroupConnection(clientConn)
	}
}

func (this *GroupForwarder) UpdateProxies() []string {
	// TODO: 可能需要加锁才能杜绝并发访问 Proxies 属性的问题
	defer func() {
		if len(this.Proxies) == 0 {
			this.Status = ForwarderStatusError
		} else {
			this.Status = ForwarderStatusOK
		}
	}()

	if this.gateway == nil {
		this.Proxies = []string{}
		return nil
	}
	clients := []string{}
	for _, loginBook := range this.gateway.manager.ProxyLogins {
		if loginBook.Proxy != nil && loginBook.Forwarder != nil && loginBook.Proxy.Group == this.Group {
			clients = append(clients, fmt.Sprintf("127.0.0.1:%d", loginBook.Forwarder.Port))
		}
	}
	this.Proxies = clients
	return this.Proxies
}

func (this *GroupForwarder) handleGroupConnection(clientConn net.Conn) {
	defer clientConn.Close()

	// 随机选择一个 Trojan-Go 客户端实例
	socks5Proxies := this.UpdateProxies()
	if len(socks5Proxies) == 0 {
		zlog.Errorf("No Trojan-Go client available for group %s", this.Group)
		return
	}
	selectedProxy := socks5Proxies[rand.Intn(len(socks5Proxies))]

	zlog.Debugf("group forwarding request, group: %s, %s => %s", this.Group, clientConn.RemoteAddr().String(), selectedProxy)

	// 连接到选定的 Trojan-Go 客户端
	serverConn, err := net.Dial("tcp", selectedProxy)
	if err != nil {
		zlog.Errorf("Failed to connect to Trojan-Go client at %s: %v", selectedProxy, err)
		return
	}
	defer serverConn.Close()

	// 启动双向数据拷贝（双向流量转发）
	done := make(chan struct{})

	go func() {
		_, err := io.Copy(clientConn, serverConn)
		if err != nil && !isClosedNetworkError(err) {
			zlog.Errorf("Error copying from server to client: %v", err)
		}
		done <- struct{}{}
	}()

	_, err = io.Copy(serverConn, clientConn)
	if err != nil && !isClosedNetworkError(err) {
		zlog.Errorf("Error copying from client to server: %v", err)
	}

	<-done // 等待双向拷贝完成
}

// 检查是否是已关闭连接的错误
func isClosedNetworkError(err error) bool {
	opErr, ok := err.(*net.OpError)
	return ok && opErr.Err.Error() == "use of closed network connection"
}

func (this *ProxyGateway) GetForwarderPorts() []string {
	ports := []string{}
	for _, loginBook := range this.manager.ProxyLogins {
		if loginBook.Forwarder != nil {
			ports = append(ports, fmt.Sprintf("%d", loginBook.Forwarder.Port))
		}
	}
	for _, gf := range this.manager.GroupForwarders {
		ports = append(ports, fmt.Sprintf("%d", gf.Port))
	}
	return ports
}

func (this *ProxyGateway) checkProxyOK(proxy *Proxy) bool {
	return proxy.Status == ProxyStatusOK
}

func (this *ProxyGateway) checkProxyDeleted(proxy *Proxy) bool {
	return proxy.Status == ProxyStatusDeleted
}

func (this *ProxyGateway) save() {
	this.manager.Save()
}

// 重置 forwarder 并且删除所有登录账户
func (this *ProxyGateway) ClearAccountsForProxy(proxyName string) {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	if proxyName == "*" {
		// Close all forwarders before clearing
		for _, loginBook := range this.manager.ProxyLogins {
			if loginBook.Forwarder != nil {
				loginBook.Forwarder.Close()
			}
		}
		this.manager.ProxyLogins = make(map[string]*ProxyLoginBook)
	} else {
		for name, loginBook := range this.manager.ProxyLogins {
			if name == proxyName {
				if loginBook.Forwarder != nil {
					loginBook.Forwarder.Close()
				}
				loginBook.Accounts = nil
				loginBook.Forwarder = nil
			}
		}
	}
	this.save()
}

func (this *ProxyGateway) SetupForwarderForProxy(proxy *Proxy, oldForwarder *Forwarder) (forwarder *Forwarder, er error) {
	if oldForwarder == nil {
		port := 0
		// pick a port for forwarder
		for i := 0; i < 3; i++ {
			port = common.PickPort("tcp", "127.0.0.1")
			existingPorts := this.GetForwarderPorts()
			if slices.Contains(existingPorts, fmt.Sprintf("%d", port)) {
				zlog.Warnf("port %d already in use, retrying...", port)
				time.Sleep(1 * time.Second)
			} else {
				break
			}
		}
		if port == 0 {
			er = fmt.Errorf("pick port failed")
			return
		}
		forwarder = &Forwarder{
			Proxy:     proxy.Name,
			Port:      port,
			StartTime: time.Now(),
		}
	} else {
		forwarder = oldForwarder
		forwarder.StartTime = time.Now()
	}

	var err error
	if proxy.Type == ProxyTypeSocks5 {
		enableUnifiedServer := false // disable unified server for HTTP/HTTPS/SOCKS5 support

		dialer, err := goproxy.SOCKS5("tcp", fmt.Sprintf("%s:%d", proxy.IP, proxy.Port), &goproxy.Auth{
			User:     proxy.Username,
			Password: proxy.Password,
		}, goproxy.Direct)
		if err != nil {
			return nil, fmt.Errorf("failed to create socks5 dialer: %v", err)
		}

		if enableUnifiedServer {
			// Start unified server instead of separate SOCKS5 server
			if err := forwarder.StartUnifiedServer(dialer); err != nil {
				return nil, fmt.Errorf("failed to start unified server: %v", err)
			}
		} else {
			// Use the old SOCKS5-only server
			conf := &socks5.Config{
				Dial: func(ctx context.Context, network, addr string) (net.Conn, error) {
					return dialer.Dial(network, addr)
				},
			}
			forwarder.socks5Server, err = socks5.New(conf)
			if err != nil {
				return nil, fmt.Errorf("failed to create socks5 server: %v", err)
			}

			go func() {
				addr := fmt.Sprintf("0.0.0.0:%d", forwarder.Port)
				if err := forwarder.socks5Server.ListenAndServe("tcp", addr); err != nil {
					zlog.Errorf("failed to start socks5 server: %v", err)
					forwarder.Status = ForwarderStatusError
				}
			}()
		}
		forwarder.Status = ForwarderStatusOK
	} else {
		clientConfig := fmt.Sprintf(`
		{
			"run_type": "client",
			"local_addr": "0.0.0.0",
			"local_port": %d,
			"remote_addr": "%s",
			"remote_port": 443,
			"password": ["%s"]
		}
		`, forwarder.Port, proxy.GetDomain(), proxy.Password)
		zlog.Debugf("client config: \n%s", clientConfig)

		forwarder.client, err = tproxy.NewProxyFromConfigData([]byte(clientConfig), true)
		if err != nil {
			er = fmt.Errorf("create forwarder failed, proxy: %s, error: %s", proxy.Name, err)
			forwarder.Status = ForwarderStatusError
			return
		}
		forwarder.Status = ForwarderStatusOK
		// start the trojan client
		go forwarder.client.Run()
	}
	return
}

// login 登录，分配一个代理（实际使用的是 forwarder）给用户
// 规则：
// 1. 对同一个 project 来说，每个登录的用户都只有一个代理（proxy）
// 2. 每个 proxy 都默认有一个 forwarder
// 3. 每个代理可能有一个或多个登录的用户
// 4. 如果用户需要启用 forwarder，那么就使用这个代理的 forwarder，否则为空
// 5. 一个 proxyGroup 可以设定每个代理上最多有几个用户登录（防止一个 ip 上登录太多用户）
func (this *ProxyGateway) Login(proxyGroup string, uid string, project string, ipAddr string) (account *LoginAccount, er error) {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	if uid == "" || this.uidExist(project, uid) {
		er = fmt.Errorf("uid can not be empty or already exist")
		return
	}

	existingAccount := this.GetAccount(uid, project)
	if existingAccount != nil {
		account = existingAccount
		return
	}

	// 不特殊设置的情况下，每个 proxy 上 account 的数量限制是 1
	accountLimit := this.getAccountLimit(project, proxyGroup)
	timeNow := time.Now()
	proxyCounts := this.getProxyCounts(proxyGroup, project)

	for _, proxyCount := range proxyCounts {
		// 如果某个 proxy 的 account 数量已经达到限制，那么跳过
		if proxyCount.Count >= accountLimit {
			zlog.Infof("proxy has reached the account limit, proxyGroup: %s, proxy: %s, proxyCount: %d, accountLimit: %d", proxyGroup, proxyCount.ProxyName, proxyCount.Count, accountLimit)
			continue
		}
		proxyName := proxyCount.ProxyName
		loginBook := this.manager.ProxyLogins[proxyName]

		account = &LoginAccount{UID: uid,
			Project:    project,
			LastActive: &timeNow,
			Proxy:      loginBook.Proxy,
			Forwarder:  loginBook.Forwarder,
			IPAddr:     ipAddr,
		}
		loginBook.Accounts = append(loginBook.Accounts, account)
		this.save()
		return
	}
	er = fmt.Errorf("no proxy available")
	return
}

// 某个 proxy 上登录了某个 project 的账户的数量统计
type ProxyAccountsCount struct {
	ProxyName  string
	ProxyGroup string
	Project    string
	Count      int
}

// 过滤属于某个 group ，某个 proxy，登录了多少个该 project 的账号
// 返回的列表，按 account 数量从小到大排序
func (this *ProxyGateway) getProxyCounts(proxyGroup string, project string) []ProxyAccountsCount {
	proxyCounts := []ProxyAccountsCount{}
	for proxyName, loginBook := range this.manager.ProxyLogins {
		if loginBook.Proxy.Group != proxyGroup {
			continue
		}
		accountCount := 0
		for _, account := range loginBook.Accounts {
			if isSameProject(account.Project, project) {
				accountCount++
			}
		}
		// 这里统计所有 proxy 的 account 数量，供后续选择
		// 这里不进行 accountLimit 过滤，因为打印统计信息之类的需要所有账号的数量
		proxyCounts = append(proxyCounts, ProxyAccountsCount{
			ProxyName:  proxyName,
			ProxyGroup: proxyGroup,
			Project:    project,
			Count:      accountCount,
		})
	}
	// 按照 account 数量从小到大排序，即某个 proxy 上面的 account 越少，越先在下一次 login 中被选择
	sort.SliceStable(proxyCounts, func(i, j int) bool {
		return proxyCounts[i].Count < proxyCounts[j].Count
	})
	return proxyCounts
}

// 获取某个 project 在某个 proxyGroup 上的 account 数量限制
// 如果 proxyGroup 为空，那么返回的是 project 的 account 数量限制
// example:
// 1. project1 设置了默认限制 2
// 2. 但是对 group1 设置了限制 3
// 3. 那么 getAccountLimit("project1", "group1") 返回 3
// 4. getAccountLimit("project1", "group2") 返回 2
func (this *ProxyGateway) getAccountLimit(project string, proxyGroup string) int {
	if limit, ok := this.manager.ProjectGroupLimits[fmt.Sprintf("%s::%s", project, proxyGroup)]; ok {
		return limit
	}
	if limit, ok := this.manager.ProjectGroupLimits[fmt.Sprintf("%s::", project)]; ok {
		return limit
	}
	return 1
}

func (this *ProxyGateway) uidExist(project string, uid string) bool {
	for _, loginBook := range this.manager.ProxyLogins {
		for _, account := range loginBook.Accounts {
			if isSameProject(account.Project, project) && account.UID == uid {
				return true
			}
		}
	}
	return false
}

func (this *ProxyGateway) Logout(uid string, project string) {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	for _, loginBook := range this.manager.ProxyLogins {
		for i, account := range loginBook.Accounts {
			if isSameProject(account.Project, project) && account.UID == uid {
				loginBook.Accounts = append(loginBook.Accounts[:i], loginBook.Accounts[i+1:]...)
			}
		}
	}
	this.save()
}

// uid 应该保持全局唯一，不需要 project 一起查询
func (this *ProxyGateway) GetAccount(uid string, project string) *LoginAccount {
	for _, loginBook := range this.manager.ProxyLogins {
		for _, account := range loginBook.Accounts {
			if isSameProject(account.Project, project) && account.UID == uid {
				return account
			}
		}
	}
	return nil
}

func (this *ProxyGateway) getShellEnv(forwarder *Forwarder) string {
	if forwarder == nil {
		return "[ERROR] forwarder is nil"
	}
	return forwarder.GetShellEnv()
}

// 根据 account 是否需要启用 forwarder 返回对应 proxy 的 forwarder 或者 nil
func (this *ProxyGateway) setupForwarderForAccount(a *LoginAccount) *Forwarder {
	timeNow := time.Now()
	for _, loginBook := range this.manager.ProxyLogins {
		for _, account := range loginBook.Accounts {
			if account.UID == a.UID {
				account.Forwarder = loginBook.Forwarder
				account.Proxy = loginBook.Proxy
				account.LastActive = &timeNow
				a.Forwarder = loginBook.Forwarder
				a.Proxy = loginBook.Proxy
				a.LastActive = &timeNow
				return a.Forwarder
			}
		}
	}
	return nil
}

func isSameProject(a, b string) bool {
	return strings.TrimPrefix(a, "_") == strings.TrimPrefix(b, "_")
}

// 列出所有已登录用户的 forwarders
func (this *ProxyGateway) GetAccountForwarders(proxy_group string, project string) map[string]*Forwarder {
	forwarders := map[string]*Forwarder{}
	for _, loginBook := range this.manager.ProxyLogins {
		if loginBook.Proxy == nil {
			continue
		}
		if proxy_group != "" && loginBook.Proxy.Group != proxy_group {
			continue
		}
		for _, account := range loginBook.Accounts {
			if project != "" && !isSameProject(account.Project, project) {
				continue
			}
			if account.Forwarder != nil {
				forwarders[account.UID] = account.Forwarder
			}
		}
	}
	return forwarders
}

//go:embed index.html
var indexHTML string

func (this *ProxyGateway) IndexHandler(c *gin.Context) {
	c.Writer.WriteString(indexHTML)
}

func (this *ProxyGateway) PingHandler(c *gin.Context) {
	uid := c.Query("uid")
	project := c.Query("project")
	account := this.GetAccount(uid, project)
	if account == nil {
		failJSON(c, "no proxy available")
		return
	}
	if account.LastActive == nil {
		failJSON(c, "account not logged in")
		return
	}
	timeNow := time.Now()
	account.LastActive = &timeNow
	okJSON(c, "pong")
}

func (this *ProxyGateway) GetForwarderHandler(c *gin.Context) {
	port := cast.ToInt(c.Query("port"))
	for _, loginBook := range this.manager.ProxyLogins {
		if loginBook.Forwarder != nil && loginBook.Forwarder.Port == port {
			okJSON(c, loginBook.Forwarder)
			return
		}
	}
	failJSON(c, "forwarder not found")
}

func (this *ProxyGateway) LoginHandler(c *gin.Context) {
	uid := c.Query("uid")
	proxy_group := c.Query("proxy_group")
	project := c.Query("project")

	remoteHost := c.Request.Host
	clientIP := c.ClientIP()

	if this.manager.Options.GatewayServer.EnableSurge {
		if clientIP != "127.0.0.1" && strings.HasPrefix(remoteHost, clientIP) {
			badRequest(c, "获取不到该电脑的真实 IP，请将 %s 加到代理服务器设置的\"忽略列表\"中。", remoteHost)
			return
		}
	}

	// 退出当前的登录再重新登录
	if uid == "" {
		badRequest(c, "uid can not be empty")
		return
	}

	// 如果用户已经登录，那么直接返回已经登录的 forwarder
	// 不要 logout 重新再登录，因为有时候需要保持登录的稳定性，不能随意退出
	existingAccount := this.GetAccount(uid, project)
	if existingAccount != nil {
		this.setupForwarderForAccount(existingAccount)
		this.updateSurgeConfig()
		if isSurgeIPRulesEnabled(existingAccount.Project) {
			okJSON(c, "please connect to surge proxy manually")
		} else {
			okJSON(c, gin.H{"forwarder": existingAccount.Forwarder, "shell_env": this.getShellEnv(existingAccount.Forwarder)})
		}
		return
	}

	account, err := this.Login(proxy_group, uid, project, clientIP)
	if account == nil {
		zlog.Errorf("login failed, error: %v", err)
		failJSON(c, "no proxy available")
		return
	}
	this.setupForwarderForAccount(account)
	this.updateSurgeConfig()

	if isSurgeIPRulesEnabled(account.Project) {
		okJSON(c, "please connect to surge proxy manually")
	} else {
		okJSON(c, gin.H{"forwarder": account.Forwarder, "shell_env": this.getShellEnv(account.Forwarder)})
	}
}

func (this *ProxyGateway) ScriptLoginHandler(c *gin.Context) {
	uid := c.Query("uid")
	project := c.Query("project")

	remoteHost := c.Request.Host
	clientIP := c.ClientIP()

	if this.manager.Options.GatewayServer.EnableSurge {
		remoteHostWithoutPort := strings.Split(remoteHost, ":")[0]
		if clientIP == "127.0.0.1" || strings.EqualFold(remoteHostWithoutPort, clientIP) {
			badRequest(c, "获取不到该电脑的真实 IP，请将 %s 加到代理服务器设置的\"忽略列表\"中。", remoteHost)
			return
		}
	}

	if uid == "" || project == "" {
		badRequest(c, "uid and project can not be empty")
		return
	}

	// 如果用户已经登录，才可以修改 scriptIPAddr
	// 如果没有登录，那么直接返回错误
	existingAccount := this.GetAccount(uid, project)
	if existingAccount == nil {
		badRequest(c, "account not logged in")
		return
	}
	existingAccount.ScriptIPAddr = clientIP
	this.setupForwarderForAccount(existingAccount)
	this.updateSurgeConfig()
	okJSON(c, fmt.Sprintf("please connect to surge proxy manually, your ip: %s", clientIP))
}

func (this *ProxyGateway) LogoutHandler(c *gin.Context) {
	uid := c.Query("uid")
	project := c.Query("project")

	if uid == "" || project == "" {
		badRequest(c, "uid and project can not be empty")
		return
	}

	// 先退出登录，然后更新 surge 配置文件
	this.Logout(uid, project)
	this.updateSurgeConfig()

	account := this.GetAccount(uid, project)
	if account != nil {
		failJSON(c, "logout failed")
		return
	}
	okJSON(c, "logout successfully")
}

func (this *ProxyGateway) ScriptLogoutHandler(c *gin.Context) {
	uid := c.Query("uid")
	project := c.Query("project")

	if uid == "" || project == "" {
		badRequest(c, "uid and project can not be empty")
		return
	}

	// 先退出登录，然后更新 surge 配置文件
	account := this.GetAccount(uid, project)
	if account == nil {
		failJSON(c, "script logout failed, account not logged in")
		return
	}
	account.ScriptIPAddr = ""
	this.updateSurgeConfig()
	okJSON(c, "script logout successfully")
}

func (this *ProxyGateway) ListAccountForwardersHandler(c *gin.Context) {
	project := c.Query("project")
	proxy_group := c.Query("proxy_group")

	forwarders := this.GetAccountForwarders(proxy_group, project)
	okJSON(c, forwarders)
}

func (this *ProxyGateway) ListAllForwardersHandler(c *gin.Context) {
	forwarders := []*Forwarder{}
	for _, loginBook := range this.manager.ProxyLogins {
		if loginBook.Forwarder != nil {
			forwarders = append(forwarders, loginBook.Forwarder)
		}
	}
	okJSON(c, forwarders)
}

func isLocal(c *gin.Context) bool {
	if c.ClientIP() == "127.0.0.1" || c.ClientIP() == "::1" {
		return true
	}
	return false
}

func (this *ProxyGateway) ListProxiesHandler(c *gin.Context) {
	proxies := this.manager.GetProxies()
	redactedProxies := redactProxies(proxies)
	if isLocal(c) {
		redactedProxies = proxies
	}
	okJSON(c, redactedProxies)
}

func redactProxies(proxies []*Proxy) []*Proxy {
	proxiesCopy := []*Proxy{}
	err := copier.Copy(&proxiesCopy, proxies)
	if err != nil {
		zlog.Errorf("copy proxies failed: %v", err)
		return nil
	}
	for _, proxy := range proxiesCopy {
		proxy.Password = "******"
	}
	return proxiesCopy
}

func (this *ProxyGateway) SurgeConfigHandler(c *gin.Context) {
	zlog.Debugf("client ip: [%s]", c.ClientIP())

	// limit only localhost can access this api
	if !isLocal(c) {
		c.String(403, "forbidden")
		return
	}

	newSurgeConfig := this.updateSurgeConfig()
	c.String(200, newSurgeConfig)
}

// updateSurgeConfig 更新 surge 配置文件，让 surge 重新加载配置文件
// 在 surge 配置文件中，有两个标记 # PROXY_START 和 # PROXY_END 之间的内容是代理列表
// 有两个标记 # RULE_START 和 # RULE_END 之间的内容是规则列表
// 通过替换这两个标记之间的内容来更新 surge 配置文件
// 仅有启用了 surge ip rules 的项目才会更新 surge 配置文件
func (this *ProxyGateway) updateSurgeConfig() (newSurgeConfig string) {
	if !this.manager.Options.GatewayServer.EnableSurge {
		return ""
	}
	if this.manager.Options.GatewayServer.SurgeConfigPath == "" || this.manager.Options.GatewayServer.SurgeReloadCmd == "" {
		return ""
	}
	accounts := []*LoginAccount{}
	for _, loginBook := range this.manager.ProxyLogins {
		for _, account := range loginBook.Accounts {
			if account.Forwarder != nil {
				added := false
				if isSurgeIPRulesEnabled(account.Project) {
					accounts = append(accounts, account)
					added = true
				}
				// 如果设置了 script ip addr，那么也加入到 surge 配置文件中，脚本对应的 ip 地址
				// 不重复添加
				if account.ScriptIPAddr != "" && !added {
					accounts = append(accounts, account)
				}
			}
		}
	}

	proxies := []string{}
	for _, account := range accounts {
		var proxy string
		if account.Proxy.Type == ProxyTypeSocks5 {
			// SOCKS5 format: proxy_name = socks5, server_ip, port, username=username, password=password
			proxy = fmt.Sprintf("%s = socks5, %s, %d, username=%s, password=%s\n",
				account.Proxy.Name, account.Proxy.IP, account.Proxy.Port,
				account.Proxy.Username, account.Proxy.Password)
		} else {
			// Trojan format: proxy_name = trojan, domain, 443, password=password
			proxy = fmt.Sprintf("%s = trojan, %s, 443, password=%s\n",
				account.Proxy.Name, account.Proxy.GetDomain(), account.Proxy.Password)
		}
		if !slices.Contains(proxies, proxy) {
			proxies = append(proxies, proxy)
		}
	}
	proxyLines := strings.Join(proxies, "")

	rules := ""
	for _, account := range accounts {
		enablePchromeIP := false
		if enablePchromeIP && account.IPAddr != "127.0.0.1" {
			rules += fmt.Sprintf("SRC-IP,%s,%s\n", account.IPAddr, account.Proxy.Name)
		}
		if account.ScriptIPAddr != "" && account.ScriptIPAddr != "127.0.0.1" {
			rules += fmt.Sprintf("SRC-IP,%s,%s\n", account.ScriptIPAddr, account.Proxy.Name)
		}
	}

	// modify include config file
	originalSurgeConfig, err := os.ReadFile(this.manager.Options.GatewayServer.SurgeConfigPath.String())
	if err != nil {
		zlog.Errorf("读取 surge 配置文件失败: %v", err)
		return ""
	}
	newSurgeConfig = string(originalSurgeConfig)
	newSurgeConfig = replaceBlock(newSurgeConfig, "# PROXY_START\n", "# PROXY_END\n", proxyLines)
	newSurgeConfig = replaceBlock(newSurgeConfig, "# RULE_START\n", "# RULE_END\n", rules)
	configFile, err := os.OpenFile(this.manager.Options.GatewayServer.SurgeConfigPath.String(), os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		zlog.Errorf("打开 surge 配置文件失败: %v", err)
		return ""
	}
	defer configFile.Close()
	_, err = configFile.WriteString(newSurgeConfig)
	if err != nil {
		zlog.Errorf("写入 surge 配置文件失败: %v", err)
		return ""
	}
	// run surge reload cmd
	if this.manager.Options.GatewayServer.SurgeReloadCmd != "" {
		cmd := strings.Split(this.manager.Options.GatewayServer.SurgeReloadCmd, " ")
		if len(cmd) > 0 {
			cmd := exec.Command(cmd[0], cmd[1:]...)
			err := cmd.Run()
			if err != nil {
				zlog.Errorf("执行 surge reload cmd 失败: %v", err)
			}
			zlog.Infof("surge successfully reloaded.")
		}
	}
	return newSurgeConfig
}

func (this *ProxyGateway) ClearSurgeConfig() (newSurgeConfig string) {
	if !this.manager.Options.GatewayServer.EnableSurge {
		return ""
	}
	if this.manager.Options.GatewayServer.SurgeConfigPath == "" || this.manager.Options.GatewayServer.SurgeReloadCmd == "" {
		return ""
	}

	// modify include config file
	originalSurgeConfig, err := os.ReadFile(this.manager.Options.GatewayServer.SurgeConfigPath.String())
	if err != nil {
		zlog.Errorf("读取 surge 配置文件失败: %v", err)
		return ""
	}
	newSurgeConfig = string(originalSurgeConfig)
	newSurgeConfig = replaceBlock(newSurgeConfig, "# PROXY_START\n", "# PROXY_END\n", "")
	newSurgeConfig = replaceBlock(newSurgeConfig, "# RULE_START\n", "# RULE_END\n", "")
	configFile, err := os.OpenFile(this.manager.Options.GatewayServer.SurgeConfigPath.String(), os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		zlog.Errorf("打开 surge 配置文件失败: %v", err)
		return ""
	}
	defer configFile.Close()
	_, err = configFile.WriteString(newSurgeConfig)
	if err != nil {
		zlog.Errorf("写入 surge 配置文件失败: %v", err)
		return ""
	}
	// run surge reload cmd
	if this.manager.Options.GatewayServer.SurgeReloadCmd != "" {
		cmd := strings.Split(this.manager.Options.GatewayServer.SurgeReloadCmd, " ")
		if len(cmd) > 0 {
			cmd := exec.Command(cmd[0], cmd[1:]...)
			err := cmd.Run()
			if err != nil {
				zlog.Errorf("执行 surge reload cmd 失败: %v", err)
			}
			zlog.Infof("surge config cleared.")
		}
	}
	return newSurgeConfig
}

// replaceBlock 替换 orig 中 startIdentifier 和 endIdentifier 之间的内容为 content
// 用于更新 proxy 和 rule 列表
func replaceBlock(orig, startIdentifier, endIdentifier string, content string) string {
	// find startIdentifier & endIdentifier in orig
	startIndex := strings.Index(orig, startIdentifier)
	if startIndex == -1 {
		return orig
	}
	endIndex := strings.Index(orig, endIdentifier)
	if endIndex == -1 {
		return orig
	}
	// replace the block
	firstPart := orig[:startIndex]
	secondPart := orig[endIndex+len(endIdentifier):]
	newContent := firstPart + startIdentifier + content + endIdentifier + secondPart
	return newContent
}

func (this *ProxyGateway) ListProxyTemplates(c *gin.Context) {
	templateNames := []string{}
	templates := this.manager.Options.Templates
	for _, t := range templates {
		templateNames = append(templateNames, t.Name)
	}
	okJSON(c, templateNames)
}

func (this *ProxyGateway) TestProxiesHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}
	this.manager.TestAllProxies()
	okJSON(c, nil)
}

func (this *ProxyGateway) CreateProxyHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	name := c.Query("name")
	if name == "" {
		badRequest(c, "name is required")
		return
	}

	template := c.Query("template")
	if template == "" {
		badRequest(c, "template is required")
		return
	}

	group := c.Query("group")

	lifeHour := cast.ToInt(c.Query("life_hour"))
	if lifeHour < 0 || lifeHour > 720*3 {
		badRequest(c, "life hour must be greater than or equal to 0 and less than 3 months")
		return
	}

	proxy, err := this.manager.CreateProxy(name, template, lifeHour, group)
	if err != nil {
		failJSON(c, "create proxies failed: %s", err)
		return
	}

	okJSON(c, proxy)
}

func (this *ProxyGateway) CreateSocks5ProxyHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	name := c.Query("name")
	if name == "" {
		badRequest(c, "name is required")
		return
	}

	port := cast.ToInt(c.Query("port"))
	if port <= 0 {
		port = 1080 // default SOCKS5 port
	}

	lifeHour := cast.ToInt(c.Query("life_hour"))
	if lifeHour < 0 || lifeHour > 720*3 {
		badRequest(c, "life hour must be greater than or equal to 0 and less than 3 months")
		return
	}

	// Get group from query parameters
	group := c.Query("group")

	// Get credentials from query parameters
	username := c.Query("username")
	password := c.Query("password")
	ip := c.Query("ip")

	// If credentials are not provided, generate random ones
	if username == "" {
		username = "user"
	}
	if password == "" {
		password = randomPassword(8)
	}
	if ip == "" {
		ip = "127.0.0.1" // SOCKS5 proxies are typically local
	}

	proxy := NewSocks5Proxy(name, username, password, ip, port, lifeHour, group)

	// Add to manager
	this.manager.AddProxy(proxy)
	this.manager.Save()

	// Start the proxy creation process
	go func() {
		err := proxy.Create()
		if err != nil {
			zlog.Errorf("create SOCKS5 proxy failed: %v", err)
		} else {
			zlog.Infof("SOCKS5 proxy created successfully: %s", name)
		}
		this.manager.Save()
		this.RefreshProxies()
	}()

	okJSON(c, proxy)
}

func (this *ProxyGateway) DeleteProxyHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	name := c.Query("name")
	if name == "" {
		badRequest(c, "name is required")
		return
	}

	err := this.manager.DeleteProxy(name)
	if err != nil {
		failJSON(c, "delete proxy failed: %s", err)
		return
	}

	okJSON(c, nil)
}

func (this *ProxyGateway) RepairProxyHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	name := c.Query("name")
	if name == "" {
		badRequest(c, "name is required")
		return
	}

	err := this.manager.RepairProxy(name)
	if err != nil {
		failJSON(c, "repair proxy failed: %s", err)
		return
	}

	okJSON(c, nil)
}

func (this *ProxyGateway) StartProxyHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	name := c.Query("name")
	if name == "" {
		badRequest(c, "name is required")
		return
	}

	err := this.manager.StartProxy(name)
	if err != nil {
		failJSON(c, "start proxy failed: %s", err)
		return
	}

	okJSON(c, nil)
}

func (this *ProxyGateway) RemoveLogForProgramHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	name := c.Query("name")
	if name == "" {
		badRequest(c, "name is required")
		return
	}

	proxies := this.manager.GetProxiesByName(name)
	for _, proxy := range proxies {
		proxy.RemoveLogForTrojan()
	}

	okJSON(c, nil)
}

func (this *ProxyGateway) StopProxyHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	name := c.Query("name")
	if name == "" {
		badRequest(c, "name is required")
		return
	}

	err := this.manager.StopProxy(name)
	if err != nil {
		failJSON(c, "stop proxy failed: %s", err)
		return
	}

	okJSON(c, nil)
}

func (this *ProxyGateway) IncreaseLifeHourHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	name := c.Query("name")
	if name == "" {
		badRequest(c, "name is required")
		return
	}

	lifeHour := cast.ToInt(c.Query("life_hour"))
	if lifeHour == 0 {
		badRequest(c, "hour is required")
		return
	}

	err := this.manager.IncreaseLifeHour(name, lifeHour)
	if err != nil {
		failJSON(c, "increase life hour failed: %s", err)
		return
	}

	okJSON(c, nil)
}

func (this *ProxyGateway) SuggestNameHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	template := c.Query("template")
	if template == "" {
		badRequest(c, "template is required")
		return
	}

	name := this.manager.SuggestName(template)
	okJSON(c, name)
}

func (this *ProxyGateway) ListProxyLoginsHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}
	logins := this.manager.ProxyLogins
	okJSON(c, logins)
}

func (this *ProxyGateway) ListGroupForwardersHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}
	forwarders := this.manager.GroupForwarders
	for _, forwarder := range forwarders {
		forwarder.UpdateProxies()
	}
	okJSON(c, forwarders)
}

func (this *ProxyGateway) CheckNameHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	name := c.Query("name")
	if name == "" {
		badRequest(c, "name is required")
		return
	}

	for _, p := range this.manager.GetProxies() {
		if p.Name == name {
			failJSON(c, "name exist")
			return
		}
	}

	okJSON(c, nil)
}

func (this *ProxyGateway) ListAWSReservedInstancesHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	awsReservedInstances, err := this.manager.GetAWSReservedInstances()
	if err != nil {
		failJSON(c, "get aws reserved instances failed: %s", err)
		return
	}

	okJSON(c, awsReservedInstances)
}

func (this *ProxyGateway) ListAWSReservationCoverageHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	days := cast.ToInt(c.Query("days"))
	coverages, err := this.manager.GetAWSReservationCoverages(days)
	if err != nil {
		failJSON(c, "get aws reservation coverage failed: %s", err)
		return
	}

	okJSON(c, coverages)
}

func (this *ProxyGateway) ListGoogleCommitmentsHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	commitments, err := this.manager.GetGoogleCommitments()
	if err != nil {
		failJSON(c, "get google commitments failed: %s", err)
		return
	}

	okJSON(c, commitments)
}

func (this *ProxyGateway) ListGoogleCommitmentsCoverageHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	coverages, err := this.manager.GetGoogleCommitmentCoverages()
	if err != nil {
		failJSON(c, "get google commitment coverage failed: %s", err)
		return
	}

	okJSON(c, coverages)
}

func (this *ProxyGateway) CheckSyncProxiesHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	fromPgateID := c.Query("from_pgate_id")
	if fromPgateID == "" {
		fromPgateID = this.manager.Options.SyncStorage.FromPgateID
	}
	if fromPgateID == "" {
		badRequest(c, "from_pgate_id is required")
		return
	}

	key := this.manager.GetSyncStorageKey(fromPgateID)
	cloudFile, err := this.manager.Options.SyncStorage.Account.DownloadFileFromS3(
		this.manager.Options.SyncStorage.Region,
		this.manager.Options.SyncStorage.Bucket,
		key,
	)
	if err != nil {
		badRequest(c, "download file from s3 failed: %s", err)
		return
	}

	// 验证格式
	err = json.Unmarshal(cloudFile, &ProxyManager{})
	if err != nil {
		badRequest(c, "invalid file format: %s", err)
		return
	}

	// 对比本地存储
	localFilePath := path.Join(this.manager.Options.DataDir, STORAGE_FILE)
	localFile, err := os.Open(localFilePath)
	if err != nil {
		badRequest(c, "open local file failed: %s", err)
		return
	}
	defer localFile.Close()
	localFileContent, err := io.ReadAll(localFile)
	if err != nil {
		badRequest(c, "read local file failed: %s", err)
		return
	}

	diff := cmp.Diff(string(localFileContent), string(cloudFile))

	okJSON(c, gin.H{"file": string(cloudFile), "diff": diff})
}

func (this *ProxyGateway) SyncProxiesHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	fromPgateID := c.Query("from_pgate_id")
	if fromPgateID == "" {
		fromPgateID = this.manager.Options.SyncStorage.FromPgateID
	}
	if fromPgateID == "" {
		badRequest(c, "from_pgate_id is required")
		return
	}

	key := this.manager.GetSyncStorageKey(fromPgateID)
	cloudFile, err := this.manager.Options.SyncStorage.Account.DownloadFileFromS3(
		this.manager.Options.SyncStorage.Region,
		this.manager.Options.SyncStorage.Bucket,
		key,
	)
	if err != nil {
		badRequest(c, "download file from s3 failed: %s", err)
		return
	}

	tmpManager := &ProxyManager{}
	err = json.Unmarshal(cloudFile, tmpManager)
	if err != nil {
		badRequest(c, "invalid file format: %s", err)
		return
	}

	// 仅同步代理，不同步 forwarder，这样不影响使用中的 forwarder
	this.manager.SetProxies(tmpManager.Proxies)
	if this.manager.Gateway != nil {
		this.manager.Gateway.RefreshProxies()
	}

	okJSON(c, nil)
}

func (this *ProxyGateway) ClearAccountsForProxyHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	proxyName := c.Query("proxy_name")
	if proxyName == "" {
		badRequest(c, "proxy_name is required")
		return
	}

	this.ClearAccountsForProxy(proxyName)
	okJSON(c, nil)
}

func (this *ProxyGateway) SetAccountLimitHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	project := c.Query("project")
	if project == "" {
		badRequest(c, "project is required")
		return
	}

	proxyGroup := c.Query("proxy_group")
	limit := cast.ToInt(c.Query("limit"))

	this.SetAccountLimit(project, proxyGroup, limit)
	okJSON(c, nil)
}

func (this *ProxyGateway) SetAccountLimit(project string, proxyGroup string, limit int) {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	if limit == 0 {
		delete(this.manager.ProjectGroupLimits, fmt.Sprintf("%s::%s", project, proxyGroup))
	} else {
		this.manager.ProjectGroupLimits[fmt.Sprintf("%s::%s", project, proxyGroup)] = limit
	}
	this.save()
}

func (this *ProxyGateway) ListAccountLimitsHandler(c *gin.Context) {
	if !isLocal(c) {
		badRequest(c, "only local access allowed")
		return
	}

	if len(this.manager.ProjectGroupLimits) == 0 {
		okJSON(c, map[string]int{})
		return
	}

	okJSON(c, this.manager.ProjectGroupLimits)
}
