package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"slices"
	"sort"
	"strings"
	"time"

	"github.com/fatih/color"
	"github.com/mitchellh/go-homedir"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/pgate"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
	"github.com/wizhodl/quanter/utils/copydir"
)

var (
	buildTime  string // 编译时间
	commitHash string // git commit hash
)

type Pchrome struct {
	ProxyGateway string                      `json:"proxy_gateway"`
	ProxyGroup   string                      `json:"proxy_group"`
	Project      string                      `json:"project"`
	Tag          string                      `json:"tag"`
	UIDs         []string                    `json:"uids"`
	PerFolder    int                         `json:"per_folder"`
	DeletedUIDs  []string                    `json:"deleted_uids"`
	Forwarders   map[string]*pgate.Forwarder `json:"forwarders"`
	ForcePort    int                         `json:"force_port"` // 强制使用指定的端口，非常危险，只有在确认不再需要使用隔离的代理环境才使用

	// needCopyData bool     // 是否需要复制用户数据，当 uids 发生变化的时候需要复制用户数据
	needCopyUIDs []string // 需要复制的 uid

	forceForwarder *pgate.Forwarder
	pgateClient    *pgate.PgateClient
}

func main() {
	zlog.Infof(getVersion())

	var proxyGateway string
	var forcePort int
	var project string
	var tag string
	flag.StringVar(&proxyGateway, "proxy_gateway", "", "代理网关的地址(host:port)")
	flag.IntVar(&forcePort, "force_port", 0, "强制使用指定的端口")
	flag.StringVar(&project, "project", "", "项目名称")
	flag.StringVar(&tag, "tag", "", "项目标签")
	flag.Parse()

	if proxyGateway == "" {
		proxyGateway = utils.SurveyInput("输入代理网关的地址(host:port)")
		if proxyGateway == "" {
			zlog.Errorf("代理网关地址不能为空")
			return
		}
	}

	if forcePort > 0 && project == "" {
		zlog.Errorf("强制指定端口时，项目名称不能为空")
		return
	}

	// 升级程序
	upgradeBinary(proxyGateway)

	s := Pchrome{
		ProxyGateway: proxyGateway,
		pgateClient:  pgate.NewPgateClient(proxyGateway),
	}

	// 创建 pchrome 目录，否则 storage 写入会失败
	pchromeRoot, _ := homedir.Expand("~/pchrome")
	err := os.MkdirAll(pchromeRoot, 0755)
	if err != nil {
		zlog.Errorf("os.MkdirAll => %v", err.Error())
		return
	}

	zlog.Infof("creating chromes with proxies")

	var envPath string
	// 没有指定 project tag，需要选择环境或新增环境
	if project == "" {
		envPath = selectEnvs()
	} else {
		// 强制使用指定的端口，非常危险，只有在确认不再需要使用隔离的代理环境才使用
		if forcePort > 0 {
			var err error
			s.forceForwarder, err = s.pgateClient.GetForwarderByPort(forcePort)
			if err != nil {
				zlog.Errorf("get forwarder by port failed, error: %v", err)
				return
			}

			t := utils.NewTable()
			t.SetHeader([]string{"Project", "Tag", "Proxy", "Port"})
			t.AddRow([]string{project, tag, s.forceForwarder.Proxy, color.MagentaString("=> %d", forcePort)})
			zlog.Infof("\n%s", t.Render())
			confirmStr := color.GreenString("force project: %s, tag: %s to use port %d of proxy %s", project, tag, forcePort, s.forceForwarder.Proxy)
			confirm := utils.SurveyInput(fmt.Sprintf("请输入 %s 强制使用端口号", confirmStr))
			if !pgate.TextEqual(confirm, confirmStr) {
				zlog.Errorf("输入不匹配，退出")
				return
			}
			confirm2Str := color.RedString("I understand this is dangerous, I know what I'm doing")
			confirm2 := utils.SurveyInput(fmt.Sprintf("请输入 %s ，最后确认", confirm2Str))
			if !pgate.TextEqual(confirm2, confirm2Str) {
				zlog.Errorf("输入不匹配，退出")
				return
			}
			envPath = fmt.Sprintf("~/pchrome/%s.json", formatProjectTag(project, tag))
			envPath, _ = homedir.Expand(envPath)
			if _, err := os.Stat(envPath); os.IsNotExist(err) {
				zlog.Errorf("项目不存在: %s", envPath)
				return
			}
		}
	}

	// 如果 envPath 为空，新建环境
	if envPath == "" {
		// 输入 proxy group 的名称
		s.ProxyGroup = utils.SurveyInput("输入要使用的代理组名称")

		for {
			// 输入项目名称 project
			s.Project = utils.SurveyInput("输入项目名称")
			if s.Project == "" {
				zlog.Errorf("项目名称不能为空")
				return
			}
			s.Tag = utils.SurveyInput("输入项目标签")

			if checkEnvExist(s.GetProjectFullName()) {
				zlog.Errorf("项目已经存在，请检查项目和标签名称")
				continue
			} else {
				break
			}
		}

		// 输入需要创建的 chrome 环境的数量
		quantity := utils.SurveyInt("chrome 环境的总数量")
		perFolder := utils.SurveyInt("每个文件夹的环境数量（默认 4 个）")
		if perFolder == 0 {
			perFolder = 4
		}
		s.PerFolder = perFolder

		// 在输入环境的 ID 之前，需要先设置好模板环境，否则可能获取不到 ID
		if s.promptSetupTemplate("请先对模板环境做必要的设置，获取对应钱包地址：", "继续") {
			return
		}

		// 输入所有 chrome 环境的 ID，或者自动生成
		_, howToGenerateID := utils.SurveySelect("如何生成 chrome 环境的 ID", []string{"手工输入", "从文件加载", "自动生成"})

		s.UIDs = []string{}
		if howToGenerateID == "自动生成" {
			// 自动生成
			for i := 0; i < quantity; i++ {
				s.UIDs = append(s.UIDs, pgate.GenerateRandomName(4, false))
			}
		} else if howToGenerateID == "手工输入" {
			ids := utils.SurveyInput("输入所有 chrome 环境的 ID，以逗号分隔 (例如： 1: 0x12345, 2: 0x54321)")
			s.UIDs = cleanupUIDs(ids)
		} else if howToGenerateID == "从文件加载" {
			// retry 3 times
			for range 3 {
				// 从文件加载
				filePath := utils.SurveyInput("输入文件路径(每个 ID 一行，~ 表示用户目录):")
				if filePath == "" {
					yes := utils.SurveyYes("是否退出")
					if yes {
						return
					}
					continue
				}
				if strings.HasPrefix(filePath, "~") {
					filePath, _ = homedir.Expand(filePath)
				}
				file, err := os.Open(filePath)
				if err != nil {
					zlog.Errorf("文件路径不存在，os.Open => %v", err.Error())
					continue
				}
				defer file.Close()

				data, err := io.ReadAll(file)
				if err != nil {
					zlog.Errorf("读取文件错误，io.ReadAll => %v", err.Error())
					continue
				}

				uids := strings.Split(string(data), "\n")
				uidStr := strings.Join(uids, ",")
				s.UIDs = cleanupUIDs(uidStr)
				break
			}
		}
		s.needCopyUIDs = append(s.needCopyUIDs, s.UIDs...)
		s.save()
	} else {
		err = s.load(envPath)
		if err != nil {
			zlog.Panicf("load env failed, path: %s, error: %v", envPath, err.Error())
			return
		}
		if s.ProxyGateway != proxyGateway {
			zlog.Errorf("proxy gateway not match, you may used wrong proxy gateway, from file: %s, cmd arg: %s", s.ProxyGateway, proxyGateway)
			return
		}

		if forcePort > 0 {
			s.ForcePort = forcePort
			s.save()
		}

		zlog.Debugf("s.ProxyGateway: %s", s.ProxyGateway)
		zlog.Debugf("s.ProxyGroup: %s", s.ProxyGroup)
		zlog.Debugf("s.Project: %s", s.Project)
		zlog.Debugf("s.Tag: %s", s.Tag)
		zlog.Debugf("s.ForcePort: %d", s.ForcePort)
		zlog.Debugf("s.UIDs: %v", s.UIDs)
		zlog.Debugf("s.PerFolder: %d", s.PerFolder)

		// 检查 forwarder 是否被删除，如果被删除，提示用户是否重新登录
		// 必须检查通过
		stop := s.promptDeletedProxies()
		if stop {
			return
		}

		// 提示新增环境
		stop = s.promptForMore()
		if stop {
			return
		}

		// 提示删除环境
		manualDelete := false
		if manualDelete {
			stop = s.promptForDelete()
			if stop {
				return
			}
		}

		// 提示退出环境
		stop = s.promptForLogout()
		if stop {
			return
		}
	}

	// 更新启动脚本
	s.createChromeEnvDirs()
	// 最后机会对模板环境做必要的设置
	if s.promptSetupTemplate("请先对模板环境做必要的设置，然后再开始复制", "开始复制") {
		return
	}
	s.checkUserDataDeleted()
	s.copyUserData()

	// 登录完成后，才能更新启动脚本，因为需要代理的信息
	if s.ForcePort == 0 {
		s.login()
	}

	err2 := s.save()
	if err2 != nil {
		zlog.Errorf("s.save => %v", err2.Error())
	}

	zlog.Infof("login status: \n\n%s", s.FormatLoginStatus())
	s.updateChromeStartupScript()

	zlog.Infof("Done.")
}

func formatProjectTag(project, tag string) string {
	if tag == "" {
		return project
	}
	return fmt.Sprintf("%s_%s", project, tag)
}

func getVersion() string {
	return fmt.Sprintf("Build: %s/(%s)", commitHash, buildTime)
}

func upgradeBinary(proxyGateway string) {
	if runtime.GOOS != "windows" {
		zlog.Warnf("only support upgrade binary in windows")
		return
	}
	programDir := "c:\\programs"
	upgradePath := filepath.Join(programDir, "upgrade_pchrome.bat")

	if err := os.Remove(upgradePath); err != nil {
		zlog.Debugf("remove upgrade script failed, error: %s", err.Error())
	}

	var bTime *time.Time
	if buildTime != "" {
		loc := time.FixedZone("CST", 8*60*60) // CST is China Standard Time, which is UTC+8
		t, err := time.ParseInLocation("2006-01-02.15:04:05", buildTime, loc)
		if err != nil {
			fmt.Printf("failed to parse build time: %s", err)
			os.Exit(1)
		}
		bTime = &t
	}

	// 检查是否有新版本
	if bTime == nil {
		zlog.Warnf("build time is empty, skip upgrade binary")
		return
	}

	response, err := httpGet(fmt.Sprintf("http://%s/pchrome/version?platform=win_arm64", proxyGateway))
	if err != nil {
		zlog.Errorf("check new version failed, error: %s", err.Error())
		return
	}

	result := gjson.Parse(response)
	if !result.Get("ok").Bool() {
		zlog.Errorf("parse version data failed: %s", result.Get("message").String())
		return
	}
	binaryBuildTime := result.Get("data.build_time").Int()
	binaryFile := result.Get("data.file").String()
	if binaryBuildTime == 0 {
		zlog.Errorf("binary mod time is empty, skip upgrade binary")
		return
	}

	// 检查是否需要升级，如果更新时间在 1 秒内，则不升级
	if binaryBuildTime-bTime.Unix() < 1 {
		zlog.Infof(color.YellowString("already up to date, no need to upgrade binary"))
		return
	}
	zlog.Debugf("build time: %v, binary mod time: %v", bTime.Unix(), binaryBuildTime)
	zlog.Infof("found new version, upgrading binary...")

	// Download new version
	resp, err := http.Get(fmt.Sprintf("http://%s/pchrome/files/%s", proxyGateway, binaryFile))
	if err != nil {
		zlog.Errorf("download binary failed, error: %v", err.Error())
		return
	}
	defer resp.Body.Close()

	newBinaryPath := filepath.Join(programDir, "pchrome_new.exe")
	oldBinaryPath := filepath.Join(programDir, "pchrome.exe")
	out, err := os.Create(newBinaryPath)
	if err != nil {
		zlog.Errorf("creating new binary failed, error: %v", err.Error())
		return
	}
	defer out.Close()

	_, err = io.Copy(out, resp.Body)
	if err != nil {
		zlog.Errorf("saving new binary file failed, error: %v", err.Error())
		return
	}
	zlog.Infof("successfully downloaded new binary to: %s", newBinaryPath)

	// Create batch script to replace binary
	batch := []byte(fmt.Sprintf(`
		:wait
		tasklist | find /i "pchrome.exe" > nul 2>&1
		if errorlevel 1 (
			move /Y %s %s
			start %s --proxy_gateway=%s
		) else (
			timeout /t 1 /nobreak > nul
			goto wait
		)
	`, newBinaryPath, oldBinaryPath, oldBinaryPath, proxyGateway))
	os.WriteFile(upgradePath, batch, 0644)
	zlog.Infof("successfully created upgrade script to: %s", upgradePath)

	// Run batch script
	cmd := exec.Command("cmd", "/C", "start", upgradePath)
	err = cmd.Start()
	if err != nil {
		zlog.Errorf("execute upgrade bat failed, error: %v", err.Error())
		return
	}

	// Exit program
	zlog.Infof(color.GreenString("upgrade binary success, exit current program"))
	os.Exit(0)
}

func checkEnvExist(fullName string) bool {
	// 读取配置文件
	p := fmt.Sprintf("~/pchrome/%s.json", fullName)
	storageFile, _ := homedir.Expand(p)

	// check file exist
	if _, err := os.Stat(storageFile); !os.IsNotExist(err) {
		return true
	}
	return false
}

func selectEnvs() string {
	// list dir
	pchromeRoot, _ := homedir.Expand("~/pchrome")
	files, err := os.ReadDir(pchromeRoot)
	if err != nil {
		zlog.Errorf("os.ReadDir => %v", err.Error())
		return ""
	}

	// filter json files
	jsonFiles := []string{}
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".json") {
			jsonFiles = append(jsonFiles, file.Name())
		}
	}
	// sort files
	sort.Strings(jsonFiles)

	// select env
	options := []string{}
	for i, file := range jsonFiles {
		options = append(options, fmt.Sprintf("%d: %s", i+1, strings.TrimSuffix(file, ".json")))
	}

	options = append(options, "a: 新增一个项目", "q: 退出")

	idx, s := utils.SurveySelect("选择一个环境", options)
	if strings.HasPrefix(s, "a:") {
		return ""
	}
	if strings.HasPrefix(s, "q:") {
		os.Exit(0)
	}
	return fmt.Sprintf("~/pchrome/%s", jsonFiles[idx])
}

func cleanupUIDs(uidStr string) []string {
	uids := strings.Split(uidStr, ",")
	newIDs := []string{}
	for _, uid := range uids {
		uid = strings.TrimSpace(uid)
		// 如果 uid 包含 # 号，则跳过
		if strings.Contains(uid, "#") {
			continue
		}
		if uid == "" {
			continue
		}
		newIDs = append(newIDs, uid)
	}
	// 清理每个单独的 uid，满足统一的 {walletID}: {address} 的格式
	cleanedUIDs := []string{}
	for _, uid := range newIDs {
		if strings.Contains(uid, ":") {
			parts := strings.Split(uid, ":")
			cleanedUIDs = append(cleanedUIDs, fmt.Sprintf("%s: %s", strings.TrimSpace(parts[0]), strings.TrimSpace(parts[1])))
		} else {
			cleanedUIDs = append(cleanedUIDs, uid)
		}
	}
	return cleanedUIDs
}

func (this *Pchrome) checkUserDataDeleted() {
	for _, uid := range this.UIDs {
		userDataDir, _ := this.getUserDataDir(uid)
		if !isDir(userDataDir) {
			this.needCopyUIDs = append(this.needCopyUIDs, uid)
			continue
		}
		isEmpty, err := isDirEmpty(userDataDir)
		if err != nil {
			zlog.Errorf("read user data dir failed, error: %s", err.Error())
			continue
		}
		if isEmpty {
			this.needCopyUIDs = append(this.needCopyUIDs, uid)
		}
	}
}

func (this *Pchrome) promptSetupTemplate(message string, next string) (stop bool) {
	// 确保模板环境存在，并在复制用户数据前先设置好模板环境
	templateDir, err := this.GetTemplateDir()
	if err != nil {
		zlog.Panicf("get default user data dir failed, abort. error: %s", err.Error())
		stop = true
		return
	}
	templateShortcut := this.updateTemplateStartupScript()

	if isEmpty, _ := isDirEmpty(templateDir); isEmpty {
		for {
			options := []string{fmt.Sprintf("1: %s", next), "q: 取消"}
			idx, _ := utils.SurveySelect(message, options)
			if idx == 0 {
				if isEmpty, _ := isDirEmpty(templateDir); isEmpty {
					zlog.Errorf("模板环境为空，请先设置模板环境(打开 => %s )", templateShortcut)
					continue
				}
				break
			} else if idx == len(options)-1 {
				zlog.Infof("已取消复制")
				stop = true
				return
			}
		}
	}
	return
}

func (this *Pchrome) promptForLogout() (stop bool) {
	// 提示退出环境
	needLogout := utils.SurveyYes("【危险】是否需要登出 pchrome 环境？")
	if needLogout {
		secondConfirmStr := color.GreenString("logout all %d accounts for project %s", len(this.UIDs), formatProjectTag(this.Project, this.Tag))
		doubleCheck := utils.SurveyInput(fmt.Sprintf("请输入: %s， 确认登出 pchrome 环境", secondConfirmStr))
		if !pgate.TextEqual(doubleCheck, secondConfirmStr) {
			zlog.Errorf("double check failed, abort logout, input: %s != %s", doubleCheck, secondConfirmStr)
			return true
		}
		thirdConfirmStr := color.RedString("I understand this is dangerous, I know what I'm doing")
		thirdCheck := utils.SurveyInput(fmt.Sprintf("请输入: %s， 最后确认", thirdConfirmStr))
		if !pgate.TextEqual(thirdCheck, thirdConfirmStr) {
			zlog.Errorf("third check failed, abort logout, input: %s != %s", thirdCheck, thirdConfirmStr)
			return true
		}

		this.logout()
		return true
	}
	zlog.Infof("Login status: \n%s", this.FormatLoginStatus())
	return false
}

func (this *Pchrome) promptForDelete() (stop bool) {
	deleteMore := utils.SurveyYes("是否需要删除 pchrome 环境")
	if deleteMore {
		// 输入需要删除的 chrome 环境的 ID
		ids := utils.SurveyInput("输入需要删除的 chrome 环境的 ID，以逗号分隔")
		deleteUIDs := cleanupUIDs(ids)
		for _, uid := range deleteUIDs {
			if _, ok := this.Forwarders[uid]; ok {
				delete(this.Forwarders, uid)
				// delete from UIDs
				for i, id := range this.UIDs {
					if id == uid {
						this.UIDs = append(this.UIDs[:i], this.UIDs[i+1:]...)
						zlog.Infof("deleted pchrome: %s", uid)
						break
					}
				}
				if !slices.Contains(this.DeletedUIDs, uid) {
					this.DeletedUIDs = append(this.DeletedUIDs, uid)
				}
			}
		}
		err := this.save()
		if err != nil {
			zlog.Errorf("save failed after delete pchromes, error: %v", err.Error())
			return true
		}
	}
	return false
}

func (this *Pchrome) promptForMore() (stop bool) {
	inputMore := utils.SurveyYes("是否需要新增 pchrome 环境")
	if inputMore {
		// 输入需要创建的 chrome 环境的数量
		quantity := utils.SurveyInt("输入创建的 pchrome 环境的数量")
		// 输入所有 chrome 环境的 ID，或者自动生成
		_, howToGenerateID := utils.SurveySelect("如何生成 chrome 环境的 ID", []string{"手工输入", "从文件加载", "自动生成"})

		newIDs := []string{}
		if howToGenerateID == "自动生成" {
			// 自动生成
			for i := 0; i < quantity; i++ {
				newIDs = append(newIDs, pgate.GenerateRandomName(4, false))
			}
		} else if howToGenerateID == "手工输入" {
			ids := utils.SurveyInput("输入所有 pchrome 环境的 ID，以逗号分隔")
			newIDs = cleanupUIDs(ids)
		} else if howToGenerateID == "从文件加载" {
			// retry 3 times
			for range 3 {
				// 从文件加载
				filePath := utils.SurveyInput("输入文件路径")
				if filePath == "" {
					yes := utils.SurveyYes("是否退出")
					if yes {
						return
					}
					continue
				}
				if strings.HasPrefix(filePath, "~") {
					filePath, _ = homedir.Expand(filePath)
				}
				file, err := os.Open(filePath)
				if err != nil {
					zlog.Errorf("文件路径不存在，os.Open => %v", err.Error())
					return true
				}
				defer file.Close()
				data, err := io.ReadAll(file)
				if err != nil {
					zlog.Errorf("读取文件错误，io.ReadAll => %v", err.Error())
					return true
				}
				uids := strings.Split(string(data), "\n")
				uidStr := strings.Join(uids, ",")
				newIDs = cleanupUIDs(uidStr)
			}
		}

		if len(newIDs) != quantity {
			zlog.Errorf("输入的 ID 数量不匹配，期望 %d 个，实际 %d 个", quantity, len(newIDs))
			return
		}
		for _, uid := range newIDs {
			if !slices.Contains(this.UIDs, uid) {
				this.UIDs = append(this.UIDs, uid)
			}
		}
		this.needCopyUIDs = append(this.needCopyUIDs, newIDs...)

		// delete from deletedUIDs
		newIDsInDeleted := []string{}
		for _, uid := range newIDs {
			for _, id := range this.DeletedUIDs {
				if id == uid {
					newIDsInDeleted = append(newIDsInDeleted, uid)
					break
				}
			}
		}
		if len(newIDsInDeleted) > 0 {
			confirm := utils.SurveyYes(fmt.Sprintf("检测到 %v 个新增 ID 在已删除列表中，是否继续新增", len(newIDsInDeleted)))
			if confirm {
				for _, uid := range newIDsInDeleted {
					for i, id := range this.DeletedUIDs {
						if id == uid {
							this.DeletedUIDs = append(this.DeletedUIDs[:i], this.DeletedUIDs[i+1:]...)
							break
						}
					}
				}
			} else {
				zlog.Infof("aborted adding new pchrome environments")
				return true
			}
		}

		err := this.save()
		if err != nil {
			zlog.Errorf("s.save => %v", err.Error())
			return true
		}
	}
	return false
}

func (this *Pchrome) promptDeletedProxies() (stop bool) {
	proxies := this.getForwarderProxies()

	deletedUIDsForForwarders := []string{}
	for uid := range this.Forwarders {
		if p, ok := proxies[uid]; ok {
			if p.Status == pgate.ProxyStatusDeleted || p.Status == pgate.ProxyStatusDeleting {
				deletedUIDsForForwarders = append(deletedUIDsForForwarders, uid)
			}
		} else {
			zlog.Panicf("internal error: proxy not found for forwarder %s", uid)
			return true
		}
	}

	if len(deletedUIDsForForwarders) == 0 {
		return false
	}

	deletedProxiesTable := exchange.NewTable()
	deletedProxiesTable.SetHeader([]string{"UID", "Proxy", "Port", "Status"})
	for _, uid := range deletedUIDsForForwarders {
		forwarder := this.Forwarders[uid]
		if forwarder != nil {
			deletedProxiesTable.AddRow([]string{uid, forwarder.Proxy, fmt.Sprintf("%d", forwarder.Port), color.RedString("Deleted")})
		}
	}
	zlog.Infof("检测到 %d 个代理已被删除\n\n%s", len(deletedUIDsForForwarders), deletedProxiesTable.Render())
	dealWithDeletedProxies := utils.SurveyYes("是否处理已删除的代理")
	if !dealWithDeletedProxies {
		return false
	}

	for _, uid := range deletedUIDsForForwarders {
		forwarder, ok := this.Forwarders[uid]
		if !ok {
			zlog.Errorf("forwarder not found for: %s", uid)
			continue
		}
		idx, _ := utils.SurveySelect(fmt.Sprintf("%s 对应的代理 %s 已被删除，是否重新登录", uid, forwarder.Proxy), []string{"不处理", "删除", "登录新代理"})
		switch idx {
		case 0:
			zlog.Debugf("forwarder is deleted, dont do anything for: %s", uid)
		case 1:
			// 删除
			delete(this.Forwarders, uid)
			if !slices.Contains(this.DeletedUIDs, uid) {
				this.DeletedUIDs = append(this.DeletedUIDs, uid)
			}
			// delete from UIDs
			for i, id := range this.UIDs {
				if id == uid {
					this.UIDs = append(this.UIDs[:i], this.UIDs[i+1:]...)
					break
				}
			}
			this.save()
		case 2:
			// 不从 UIDs 中删除就可以登录一个新的代理
			zlog.Infof("forwarder is deleted, dont do anything to login a new proxy for: %s", uid)
		}
	}
	return false
}

func isDir(path string) bool {
	stat, err := os.Stat(path)
	if err != nil {
		return false
	}
	return stat.IsDir()
}

func isDirEmpty(dir string) (bool, error) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return false, err
	}
	return len(entries) == 0, nil
}

func isChromeRunning() bool {
	if runtime.GOOS != "windows" {
		return false
	}
	// 执行 tasklist 命令
	cmd := exec.Command("tasklist")

	// 获取命令输出
	output, err := cmd.Output()

	if err != nil {
		fmt.Println(err)
		return true
	}

	// 将命令输出转换为字符串
	outputStr := string(output[:])

	// 检查输出中是否包含 "chrome.exe"
	lines := strings.Split(outputStr, "\n")
	// 遍历每一行，因为 pchrome.exe 中也包含了 chrome.exe 不能直接用 contains 来判断
	for _, line := range lines {
		line = strings.ToLower(strings.TrimSpace(line))
		if strings.HasPrefix(line, "chrome.exe") {
			return true
		}
	}
	return false
}

func promptCloseChrome() {
	if runtime.GOOS != "windows" {
		return
	}
	if isChromeRunning() {
		utils.SurveyInput("请确保 template 的 chrome 窗口已经关闭，按回车继续")
	}
}

func (this *Pchrome) GetTemplateDir() (string, error) {
	templateDir, _ := homedir.Expand(fmt.Sprintf("~/pchrome/user_data/%s/_template", this.GetProjectFullName()))
	// create if not exist
	if !isDir(templateDir) {
		err := os.MkdirAll(templateDir, 0755)
		if err != nil {
			zlog.Errorf("os.MkdirAll => %v", err.Error())
			return "", fmt.Errorf("template dir isn't exist and create it failed")
		}
	}
	return templateDir, nil
}

func (this *Pchrome) copyUserData() {
	if runtime.GOOS == "darwin" {
		zlog.Errorf("copy user data in macOS not supported yet")
		return
	}

	var userDataDir string

	/*
		始终从用户系统环境下复制 chrome 环境并不是个好主意，
		始终拷贝 ~/pchrome/default 下的环境
	*/

	templateDir, err := this.GetTemplateDir()
	if err != nil {
		zlog.Errorf("get default user data dir failed, abort. error: %s", err.Error())
		return
	}

	// check if template dir is empty
	isEmpty, err := isDirEmpty(templateDir)
	if err != nil {
		zlog.Errorf("read default user data dir failed, abort. dir: %s, error: %s", templateDir, err.Error())
		return
	}
	// 只有在 default 隔离环境不为空的情况下才使用
	if isEmpty {
		zlog.Errorf(color.RedString("default chrome environment is empty, skip copying user data"))
		return
	}
	userDataDir, _ = homedir.Expand(templateDir)

	if len(this.UIDs) == 0 {
		zlog.Errorf(color.RedString("no chrome environment found, skip copying user data"))
		return
	}
	for _, uid := range this.needCopyUIDs {
		zlog.Debugf("copying user data for: %s", uid)
		newUserDataDir, _ := this.getUserDataDir(uid)
		err := os.MkdirAll(newUserDataDir, 0755)
		if err != nil {
			zlog.Errorf("os.MkdirAll => %v", err.Error())
		}
		// 判断目录是否为空，如果为空则复制
		isDirEmpty, err := isDirEmpty(newUserDataDir)
		if err != nil {
			zlog.Errorf("read user data dir failed, skip for: %s, error: %s", uid, err.Error())
			continue
		}
		if isDirEmpty {
			promptCloseChrome()
			err = copydir.CopyDir(newUserDataDir, userDataDir)
			if err != nil {
				zlog.Errorf("copy dir failed for: %s, error: %s", uid, err.Error())
			}
			zlog.Debugf(color.GreenString(">>> user data copied for: %s", uid))
		} else {
			zlog.Debugf(color.YellowString("user data dir not empty, skip copying for: %s", uid))
		}
	}
}

func (this *Pchrome) GetProjectFullName() string {
	return formatProjectTag(this.Project, this.Tag)
}

func (this *Pchrome) save() error {
	// 读取配置文件
	projectTag := this.GetProjectFullName()
	if projectTag == "" {
		return fmt.Errorf("project tag is empty")
	}
	p := fmt.Sprintf("~/pchrome/%s.json", projectTag)
	storageFile, _ := homedir.Expand(p)
	// check if file exist, if not create it
	if _, err := os.Stat(storageFile); os.IsNotExist(err) {
		// create file
		file, err := os.Create(storageFile)
		if err != nil {
			zlog.Errorf("create storage file failed => %v", err.Error())
			return fmt.Errorf("os.Create => %v", err.Error())
		}
		file.Close()
	}

	// save to storage file
	if data, err := json.MarshalIndent(this, "", "    "); err != nil {
		return fmt.Errorf("json.MarshalIndent => %v", err.Error())
	} else {
		err := os.WriteFile(storageFile, data, 0755)
		if err != nil {
			return fmt.Errorf("os.WriteFile => %v", err.Error())
		}
	}
	return nil
}

func (this *Pchrome) load(path string) error {
	if path == "" {
		return fmt.Errorf("path is empty")
	}

	// 读取配置文件
	storageFile, _ := homedir.Expand(path)

	// load from storage file
	file, err := os.Open(storageFile)
	if err != nil {
		return fmt.Errorf("os.Open => %v", err.Error())
	}
	defer file.Close()
	decoder := json.NewDecoder(file)
	err = decoder.Decode(this)
	if err != nil {
		return fmt.Errorf("decoder.Decode => %v", err.Error())
	}

	zlog.Infof("load config from: %s", storageFile)
	return nil
}

func (this *Pchrome) login() error {
	forwarders, err := fetchForwarders(this.ProxyGateway, this.ProxyGroup, this.Project)
	if err != nil {
		return fmt.Errorf("fetchForwarders => %v", err.Error())
	}
	this.Forwarders = forwarders

	for _, uid := range this.UIDs {
		if _, ok := this.Forwarders[uid]; ok {
			// 已经登录过了
			zlog.Debugf("login success for: %s", uid)
			continue
		}
		// 登录到 proxy gateway
		forwarder, err := login(this.ProxyGateway, this.ProxyGroup, uid, this.Project)
		if err != nil {
			zlog.Errorf("login failed for: %s, error: %s", uid, err.Error())
			continue
		}
		this.Forwarders[uid] = forwarder
		zlog.Debugf("login success for: %s", uid)
	}

	return nil
}

func (this *Pchrome) logout() error {
	for _, uid := range this.UIDs {
		err := logout(this.ProxyGateway, uid, this.Project)
		if err != nil {
			zlog.Errorf("logout failed for: %s, error: %s", uid, err.Error())
		}
		delete(this.Forwarders, uid)
	}
	return nil
}

func (this *Pchrome) FormatLoginStatus() string {
	if this.forceForwarder == nil && this.ForcePort > 0 {
		forwarder, err := this.pgateClient.GetForwarderByPort(this.ForcePort)
		if err != nil {
			zlog.Errorf("get forwarder by port failed, error: %v", err)
			return ""
		}
		this.forceForwarder = forwarder
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"UID", "Proxy", "Port", "Login Status"})
	for _, uid := range this.UIDs {
		forwarder := this.forceForwarder
		if forwarder == nil {
			forwarder = this.Forwarders[uid]
		}

		if forwarder != nil {
			t.AddRow([]string{uid, forwarder.Proxy, fmt.Sprintf("%d", forwarder.Port), color.GreenString("OK")})
		} else {
			t.AddRow([]string{uid, "", "", color.RedString("FAILED")})
			continue
		}
	}

	if len(t.Rows) > 1 {
		return t.Render()
	} else {
		return color.YellowString("no login status found")
	}
}

func (this *Pchrome) getForwarderProxies() map[string]*pgate.Proxy {
	// 获取 proxy group 的所有 forwarder
	response, err := httpGet(fmt.Sprintf("http://%s/proxies?type=all", this.ProxyGateway))
	if err != nil {
		return nil
	}
	result := gjson.Parse(response)
	if !result.Get("ok").Bool() {
		return nil
	}
	proxiesStr := result.Get("data").String()
	proxies := []*pgate.Proxy{}
	json.Unmarshal([]byte(proxiesStr), &proxies)

	proxiesMap := map[string]*pgate.Proxy{}
	for uid, forwarder := range this.Forwarders {
		for _, p := range proxies {
			if forwarder.Proxy == p.Name {
				proxiesMap[uid] = p
				break
			}
		}
	}
	return proxiesMap
}

func (this *Pchrome) getUserDataDir(uid string) (userDataDir string, er error) {
	userDataDir, er = homedir.Expand(fmt.Sprintf("~/pchrome/user_data/%s/%s", this.GetProjectFullName(), strings.Replace(uid, ":", "", -1)))
	return
}

func (this *Pchrome) createChromeEnvDirs() {
	// 创建 chrome 环境的 user data 目录
	for _, uid := range this.UIDs {
		userDataDir, _ := this.getUserDataDir(uid)
		if !isDir(userDataDir) {
			err := os.MkdirAll(userDataDir, 0755)
			if err != nil {
				zlog.Errorf("os.MkdirAll => %v", err.Error())
			}
		}
	}

	// 创建 chrome 环境的启动脚本
	folderCount := len(this.UIDs) / this.PerFolder
	if len(this.UIDs)%this.PerFolder > 0 {
		folderCount++
	}
	// 在桌面创建启动目录和启动脚本
	desktopDir, _ := homedir.Expand("~/Desktop")
	startupDir := fmt.Sprintf("%s/%s", desktopDir, this.GetProjectFullName())
	if !isDir(startupDir) {
		err := os.MkdirAll(startupDir, 0755)
		if err != nil {
			zlog.Errorf("os.MkdirAll => %v", err.Error())
		}
	}
	allShortcutDir := fmt.Sprintf("%s/all", startupDir)
	if !isDir(allShortcutDir) {
		err := os.MkdirAll(allShortcutDir, 0755)
		if err != nil {
			zlog.Errorf("os.MkdirAll => %v", err.Error())
		}
	}
	// 在项目目录下创建快捷方式目录
	for i := 1; i <= folderCount; i++ {
		shortcutFolder := fmt.Sprintf("%s/%d", startupDir, i)
		if !isDir(shortcutFolder) {
			err := os.MkdirAll(shortcutFolder, 0755)
			if err != nil {
				zlog.Errorf("os.MkdirAll => %v", err.Error())
			}
		}
	}
}

func (this *Pchrome) updateTemplateStartupScript() string {
	// 在 macOS 下复制环境不能工作，因为 user data 插件数据会串掉
	// 在桌面创建启动目录和启动脚本
	desktopDir, _ := homedir.Expand("~/Desktop")
	shortcutDir := fmt.Sprintf("%s/%s", desktopDir, this.GetProjectFullName())
	if !isDir(shortcutDir) {
		err := os.MkdirAll(shortcutDir, 0755)
		if err != nil {
			zlog.Errorf("create template startup dir failed, error: %v", err.Error())
		}
	}
	shortcutPath := fmt.Sprintf("%s/%s/template.bat", desktopDir, this.GetProjectFullName())
	if runtime.GOOS == "darwin" {
		shortcutPath = fmt.Sprintf("%s/%s/template.command", desktopDir, this.GetProjectFullName())
	}
	templateUserDataDir, err := this.GetTemplateDir()
	if err != nil {
		zlog.Panicf("get template user data dir failed, abort. error: %s", err.Error())
		return ""
	}

	shortcutContent := fmt.Sprintf("start chrome --disable-features=UseEcoQoSForBackgroundProcess --user-data-dir=\"%s\" --proxy-bypass-list=\"<local>;%s\" --start-maximized", templateUserDataDir, this.ProxyGateway)
	if runtime.GOOS == "darwin" {
		shortcutContent = fmt.Sprintf("(/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --user-data-dir=\"%s\" --proxy-bypass-list=\"<local>;%s\" &)", templateUserDataDir, this.ProxyGateway)
	}
	err = os.WriteFile(shortcutPath, []byte(shortcutContent), 0755)
	if err != nil {
		zlog.Errorf("os.WriteFile => %v", err.Error())
	}
	// change file mode
	if runtime.GOOS == "darwin" {
		err = os.Chmod(shortcutPath, 0755)
		if err != nil {
			zlog.Errorf("os.Chmod => %v", err.Error())
		}
	}
	return shortcutPath
}

func (this *Pchrome) updateChromeStartupScript() {
	// 在桌面创建启动目录和启动脚本
	desktopDir, _ := homedir.Expand("~/Desktop")
	startupDir := fmt.Sprintf("%s/%s", desktopDir, this.GetProjectFullName())
	if !isDir(startupDir) {
		err := os.MkdirAll(startupDir, 0755)
		if err != nil {
			zlog.Errorf("os.MkdirAll => %v", err.Error())
		}
	}

	oneFileAllShortcutPath := fmt.Sprintf("%s/all/all.bat", startupDir)
	if runtime.GOOS == "darwin" {
		oneFileAllShortcutPath = fmt.Sprintf("%s/all/all.command", startupDir)
	}
	oneFileAllShortcutContent := ""

	for i, uid := range this.UIDs {
		userDataDir, _ := this.getUserDataDir(uid)

		// 创建启动脚本
		folderCount := i/this.PerFolder + 1
		shortcutFolder := fmt.Sprintf("%s/%d", startupDir, folderCount)
		allShortcutFolder := fmt.Sprintf("%s/all", startupDir)
		shortUID := uid
		if len(uid) > 4 {
			shortUID = uid[len(uid)-4:]
		} else {
			shortUID = uid[:]
		}
		walletID := fmt.Sprintf("%d", i+1)
		if strings.Contains(uid, ": ") {
			parts := strings.Split(uid, ": ")
			walletID = parts[0]
		}
		shortcutPath := fmt.Sprintf("%s/%s_%s_%s.bat", shortcutFolder, this.Project, walletID, shortUID)
		allShortcutPath := fmt.Sprintf("%s/%s_%s_%s.bat", allShortcutFolder, this.Project, walletID, shortUID)
		if runtime.GOOS == "darwin" {
			shortcutPath = fmt.Sprintf("%s/%s_%s_%s.command", shortcutFolder, this.Project, walletID, shortUID)
		}
		// 写入启动脚本
		proxyStr := "--proxy-server=\"127.0.0.1:0000\"" // 故意设置一个无效的代理，防止没有拿到代理时直接启动
		// parse ip from proxy gateway
		gatewayIP := strings.Split(this.ProxyGateway, ":")[0]
		if this.ForcePort > 0 {
			proxyStr = fmt.Sprintf("--proxy-server=\"socks5://%s:%d\"", gatewayIP, this.ForcePort)
		} else {
			forwarder, ok := this.Forwarders[uid]
			if ok && forwarder.Port > 0 {
				proxyStr = fmt.Sprintf("--proxy-server=\"socks5://%s:%d\"", gatewayIP, forwarder.Port)
			}
		}
		shortcutContent := fmt.Sprintf("start chrome --disable-features=UseEcoQoSForBackgroundProcess --user-data-dir=\"%s\" %s --proxy-bypass-list=\"<local>;%s\" --start-maximized", userDataDir, proxyStr, this.ProxyGateway)
		if runtime.GOOS == "darwin" {
			shortcutContent = fmt.Sprintf("(/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --user-data-dir=\"%s\" %s --proxy-bypass-list=\"<local>;%s\" &)", userDataDir, proxyStr, this.ProxyGateway)
		}
		oneFileAllShortcutContent += fmt.Sprintf("%s\n", shortcutContent)
		if runtime.GOOS == "windows" {
			oneFileAllShortcutContent += "timeout /t 1 /nobreak > nul\n"
		} else {
			oneFileAllShortcutContent += "sleep 1\n"
		}
		// 写入分文件夹的快捷方式
		err := os.WriteFile(shortcutPath, []byte(shortcutContent), 0755)
		if err != nil {
			zlog.Errorf("os.WriteFile => %v", err.Error())
		}
		// change file mode
		if runtime.GOOS == "darwin" {
			err = os.Chmod(shortcutPath, 0755)
			if err != nil {
				zlog.Errorf("os.Chmod => %v", err.Error())
			}
		}
		// 写入 all 文件夹的快捷方式
		err = os.WriteFile(allShortcutPath, []byte(shortcutContent), 0755)
		if err != nil {
			zlog.Errorf("os.WriteFile => %v", err.Error())
		}
		// change file mode
		if runtime.GOOS == "darwin" {
			err = os.Chmod(allShortcutPath, 0755)
			if err != nil {
				zlog.Errorf("os.Chmod => %v", err.Error())
			}
		}
	}
	// 将所有顺序启动的内容写入文件
	err := os.WriteFile(oneFileAllShortcutPath, []byte(oneFileAllShortcutContent), 0755)
	if err != nil {
		zlog.Errorf("os.WriteFile => %v", err.Error())
	}
	// change file mode
	if runtime.GOOS == "darwin" {
		err = os.Chmod(oneFileAllShortcutPath, 0755)
		if err != nil {
			zlog.Errorf("os.Chmod => %v", err.Error())
		}
	}
}

func login(gatewayServer, proxyGroup, uid, project string) (forwarder *pgate.Forwarder, er error) {
	// 登录到 proxy gateway
	response, err := httpGet(fmt.Sprintf("http://%s/login?uid=%s&proxy_group=%s&project=%s", gatewayServer, url.QueryEscape(uid), proxyGroup, project))
	if err != nil {
		er = fmt.Errorf("http.Get => %v", err.Error())
		return
	}
	zlog.Debugf("response: %s", response)
	result := gjson.Parse(response)
	if !result.Get("ok").Bool() {
		er = fmt.Errorf("login failed: %s", result.Get("message").String())
		return
	}
	forwarderStr := result.Get("data.forwarder").String()
	zlog.Debugf("forwarderStr: %s", forwarderStr)
	forwarder = &pgate.Forwarder{}
	json.Unmarshal([]byte(forwarderStr), &forwarder)
	return
}

func logout(gatewayServer, uid, project string) (er error) {
	// 登出 proxy gateway
	response, err := httpGet(fmt.Sprintf("http://%s/logout?uid=%s&project=%s", gatewayServer, uid, project))
	if err != nil {
		er = fmt.Errorf("http.Get => %v", err.Error())
		return
	}
	zlog.Debugf("response: %s", response)
	result := gjson.Parse(response)
	if !result.Get("ok").Bool() {
		er = fmt.Errorf("logout failed: %s", result.Get("message").String())
		return
	}
	return
}

func fetchForwarders(gateway_server string, proxy_group, project string) (forwarders map[string]*pgate.Forwarder, er error) {
	// 获取 proxy group 的所有 forwarder
	response, err := httpGet(fmt.Sprintf("http://%s/forwarders?proxy_group=%s&project=%s", gateway_server, proxy_group, project))
	if err != nil {
		er = fmt.Errorf("http.Get => %v", err.Error())
		return
	}
	zlog.Debugf("response: %s", response)
	result := gjson.Parse(response)
	if !result.Get("ok").Bool() {
		er = fmt.Errorf("login failed: %s", result.Get("message").String())
		return
	}
	forwardersStr := result.Get("data").String()
	forwarders = map[string]*pgate.Forwarder{}
	json.Unmarshal([]byte(forwardersStr), &forwarders)
	return
}

func httpGet(url string) (bodyStr string, er error) {
	// 发送 GET 请求
	resp, err := http.Get(url)
	if err != nil {
		er = fmt.Errorf("http.Get => %v", err.Error())
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		er = fmt.Errorf("ioutil.ReadAll => %v", err.Error())
		return
	}

	bodyStr = string(body)
	// 返回请求结果
	return
}
