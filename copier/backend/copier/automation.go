package copier

import (
	"copier/backend/logger"
	_ "embed"
	"fmt"
	"math/rand"
	"os"
	"os/exec"
	"runtime"
	"sort"
	"time"

	"copier/backend/utils"

	"github.com/jinzhu/copier"
	"github.com/mitchellh/go-homedir"
	"golang.org/x/exp/slices"
)

//go:embed automation.py
var embeddedAutomationScript string

// findPythonExecutable tries to find a working Python executable
func findPythonExecutable() string {
	candidates := []string{}

	// On Windows, try common installation paths
	if runtime.GOOS == "windows" {
		candidates = append(candidates,
			"C:/Python311/python.exe",
			"C:/Python312/python.exe",
			"C:/Python313/python.exe",
			"~/AppData/Local/Programs/Python/Python311/python.exe",
			"~/AppData/Local/Programs/Python/Python312/python.exe",
			"~/AppData/Local/Programs/Python/Python313/python.exe",
		)
	}

	for _, candidate := range candidates {
		expandedCandidate, err := homedir.Expand(candidate)
		if err != nil {
			logger.Errorf("failed to expand candidate: %v", err)
			continue
		}
		logger.Infof("checking candidate: %s -> %s", candidate, expandedCandidate)

		// For absolute paths, check if file exists
		if _, err := os.Stat(expandedCandidate); err == nil {
			return expandedCandidate
		}
	}

	return "python3" // fallback
}

// runAutomationScript executes the Python script with optional arguments
func (this *CopierController) runAutomationScript(args ...string) error {
	// Find Python executable
	pythonExe := findPythonExecutable()
	logger.Infof("using python executable: %s", pythonExe)

	var scriptPath string
	if this.options.Debug {
		scriptPath = this.options.AutomationScriptPath
	} else {
		tmpFile, err := os.CreateTemp("", "automation_*.py")
		if err != nil {
			return fmt.Errorf("failed to create temporary file: %v", err)
		}
		defer os.Remove(tmpFile.Name())

		if _, err := tmpFile.WriteString(embeddedAutomationScript); err != nil {
			return fmt.Errorf("failed to write script to temporary file: %v", err)
		}
		if err := tmpFile.Close(); err != nil {
			return fmt.Errorf("failed to close temporary file: %v", err)
		}
		scriptPath = tmpFile.Name()
	}

	// Add extension directory argument if available
	if this.options.ExtensionDir != "" {
		args = append([]string{"--extension-dir", this.options.ExtensionDir}, args...)
	}

	cmd := exec.Command(pythonExe, append([]string{scriptPath}, args...)...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("script execution failed: %v, output: %s", err, string(output))
	}
	return nil
}

// clickExtension finds and clicks the extension icon on the current screen
func (this *CopierController) clickExtension() error {
	return this.runAutomationScript()
}

// checkExtensionStatus verifies that exactly 2 extension icons are present
func (this *CopierController) checkExtensionStatus() error {
	return this.runAutomationScript("--check-status")
}

func (this *CopierController) ensureExtensionOpened() error {
	// only run in windows
	if runtime.GOOS != "windows" {
		return nil
	}
	err := this.checkExtensionStatus()
	if err != nil {
		logger.Errorf("failed to check extension status: %v, click extension", err)
		return this.clickExtension()
	}
	return nil
}

type AutomationStatus string

const (
	AutomationStatusNew           AutomationStatus = "new"
	AutomationStatusPrepared      AutomationStatus = "prepared"
	AutomationStatusPrepareFailed AutomationStatus = "prepare_failed"
	AutomationStatusRunning       AutomationStatus = "running"
	AutomationStatusSuccess       AutomationStatus = "success"
	AutomationStatusError         AutomationStatus = "error"
	AutomationStatusTimeout       AutomationStatus = "timeout"
)

type AutomationItem struct {
	RefID            string           `json:"refID"`
	ProjectID        string           `json:"projectID"`
	DailyPerformance *Performance     `json:"dailyPerformance"`
	Status           AutomationStatus `json:"status"`
	Comment          string           `json:"comment"`
	UpdateTime       time.Time        `json:"updateTime"`
}

func (this *CopierController) NewAutomationItem(schedule *AutomationSchedule, projectID string) (*AutomationItem, error) {
	project, ok := this.Projects.Load(projectID)
	if !ok {
		return nil, fmt.Errorf("project not found")
	}
	if schedule.Job == ScheduleJobDefault && !this.IsProjectRunnable(project) {
		return nil, fmt.Errorf("project is not runnable")
	}

	// Deep copy the performance data to avoid pointer issues
	dailyPerformance := &Performance{}
	if project.TodayPerformance != nil {
		copier.Copy(dailyPerformance, project.TodayPerformance)
		dailyPerformance.AgentTrades = nil
	}

	item := &AutomationItem{
		RefID:            utils.NewRandomID(),
		ProjectID:        projectID,
		DailyPerformance: dailyPerformance,
		Status:           AutomationStatusNew,
		Comment:          "",
		UpdateTime:       time.Now(),
	}
	return item, nil
}

type AutomationItems []*AutomationItem

type AutomationErrorEventType string

const (
	AutomationErrorEventTypePrepareFailed AutomationErrorEventType = "prepare_failed"
	AutomationErrorEventTypeRunningFailed AutomationErrorEventType = "running_failed"
	AutomationErrorEventTypeError         AutomationErrorEventType = "error"
)

type AutomationErrorEvent struct {
	ProjectID string                   `json:"projectID"`
	UserID    string                   `json:"userID"`
	Type      AutomationErrorEventType `json:"type"`
	Time      time.Time                `json:"time"`
	Comment   string                   `json:"comment"`
}

type AutomationBatch struct {
	RefID       string                  `json:"refID"`
	Items       AutomationItems         `json:"items"`
	StartTime   *time.Time              `json:"startTime"`
	EndTime     *time.Time              `json:"endTime"`
	WaitMinutes int                     `json:"waitMinutes"`
	ErrorEvents []*AutomationErrorEvent `json:"errorEvents"`
	RunningTime time.Duration           `json:"runningTime"`
}

type ScheduleJob string

const (
	ScheduleJobDefault          ScheduleJob = ""
	ScheduleJobUpgradeExtension ScheduleJob = "upgrade_extension"
	ScheduleJobUpdateStats      ScheduleJob = "update_stats"
)

type AutomationSchedule struct {
	RefID            string             `json:"refID"`
	Date             string             `json:"date"`
	Job              ScheduleJob        `json:"job"`
	DailyVolumeLimit float64            `json:"dailyVolumeLimit"`
	Batches          []*AutomationBatch `json:"batches"`
	CreateTime       time.Time          `json:"createTime"`
	UpdateTime       time.Time          `json:"updateTime"`
	StartTime        *time.Time         `json:"startTime"`
	EndTime          *time.Time         `json:"endTime"`
	CancelTime       *time.Time         `json:"cancelTime"`
	PauseTime        *time.Time         `json:"pauseTime"`
	CurrentBatch     *AutomationBatch   `json:"currentBatch,omitempty"`
}

type AutomationSchedules []*AutomationSchedule

func (this AutomationSchedules) CloneWithoutCurrentBatch() AutomationSchedules {
	schedules := make(AutomationSchedules, len(this))
	copier.CopyWithOption(&schedules, &this, copier.Option{IgnoreEmpty: true})
	for _, schedule := range schedules {
		schedule.CurrentBatch = nil
	}
	return schedules
}

func (this AutomationSchedules) GetActiveSchedule() *AutomationSchedule {
	date := time.Now().UTC().Format("2006-01-02")
	sort.Slice(this, func(i, j int) bool {
		return this[i].CreateTime.After(this[j].CreateTime)
	})
	for _, schedule := range this {
		if schedule.Date == date && !schedule.IsEnded() {
			return schedule
		}
	}
	return nil
}

func (this *AutomationSchedule) IsEnded() bool {
	return this.EndTime != nil || this.CancelTime != nil
}

func (this *CopierController) NewAutomationSchedule(job ScheduleJob) (schedule *AutomationSchedule) {
	if this.options.AutomationBatchRandom {
		schedule = this._newAutomationScheduleRandom(job)
	} else {
		schedule = this._newAutomationSchedule(job)
	}
	if schedule.Job == ScheduleJobDefault {
		schedule.DailyVolumeLimit = this.options.AutoStopDailyVolume
	}

	return
}

// random schedules, only for default job
func (this *CopierController) _newAutomationScheduleRandom(job ScheduleJob) *AutomationSchedule {
	schedule := &AutomationSchedule{
		RefID:      utils.NewRandomID(),
		Job:        job,
		Batches:    []*AutomationBatch{},
		Date:       time.Now().UTC().Format("2006-01-02"),
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	projectGroups := this.options.AutomationProjectGroups.Groups()
	projects := this.GetProjects(true)

	batchLimit := this.options.AutomationBatchLimit
	if slices.Contains([]ScheduleJob{ScheduleJobUpgradeExtension, ScheduleJobUpdateStats}, job) {
		batchLimit = 5
	}

	// If AutomationBatchLimit > 0, filter projects by groups and randomize order
	if batchLimit > 0 {
		// Filter projects based on automation project groups
		var filteredProjects []*Project
		if len(projectGroups) > 0 {
			for _, project := range projects {
				projectGroup := project.GetGroup()
				if slices.Contains(projectGroups, projectGroup) && this.IsProjectRunnable(project) {
					filteredProjects = append(filteredProjects, project)
				}
			}
		} else {
			filteredProjects = projects
		}

		// Extract project IDs and randomize order
		projectIDs := make([]string, len(filteredProjects))
		for i, project := range filteredProjects {
			projectIDs[i] = project.ProjectID
		}
		rand.Shuffle(len(projectIDs), func(i, j int) {
			projectIDs[i], projectIDs[j] = projectIDs[j], projectIDs[i]
		})

		// Split into batches based on batchLimit
		if len(projectIDs) > batchLimit {
			for i := 0; i < len(projectIDs); i += batchLimit {
				end := min(i+batchLimit, len(projectIDs))
				batchProjectIDs := projectIDs[i:end]
				batch := this.NewAutomationBatch(schedule, batchProjectIDs)
				schedule.Batches = append(schedule.Batches, batch)
			}
		} else {
			// Create a single batch with all projects
			batch := this.NewAutomationBatch(schedule, projectIDs)
			schedule.Batches = append(schedule.Batches, batch)
		}
		return schedule
	}
	// For AutomationBatchLimit = 0, create batches by group
	// Group projects by their group
	groupProjects := make(map[string][]*Project)
	for _, project := range projects {
		group := project.GetGroup()
		if slices.Contains(projectGroups, group) {
			groupProjects[group] = append(groupProjects[group], project)
		}
	}

	// Create a batch for each group
	for _, group := range projectGroups {
		groupProjectList, ok := groupProjects[group]
		if !ok {
			continue
		}
		// Sort projects within each group by ProjectID
		sort.Slice(groupProjectList, func(i, j int) bool {
			return groupProjectList[i].ProjectID < groupProjectList[j].ProjectID
		})

		// Extract project IDs for this group
		groupProjectIDs := make([]string, len(groupProjectList))
		for i, project := range groupProjectList {
			groupProjectIDs[i] = project.ProjectID
		}

		batch := this.NewAutomationBatch(schedule, groupProjectIDs)
		schedule.Batches = append(schedule.Batches, batch)
	}
	return schedule
}

// order projects by project groups, then by project ID alphabetically
func (this *CopierController) _newAutomationSchedule(job ScheduleJob) *AutomationSchedule {
	schedule := &AutomationSchedule{
		RefID:      utils.NewRandomID(),
		Job:        job,
		Batches:    []*AutomationBatch{},
		Date:       time.Now().UTC().Format("2006-01-02"),
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	projectGroups := this.options.AutomationProjectGroups.Groups()
	projects := this.GetProjects(true)

	batchLimit := this.options.AutomationBatchLimit
	if slices.Contains([]ScheduleJob{ScheduleJobUpgradeExtension, ScheduleJobUpdateStats}, job) {
		batchLimit = 5
	}

	if batchLimit > 0 {
		// Filter projects based on automation project groups
		var filteredProjects []*Project
		if len(projectGroups) > 0 {
			for _, project := range projects {
				projectGroup := project.GetGroup()
				if slices.Contains(projectGroups, projectGroup) && this.IsProjectRunnable(project) {
					filteredProjects = append(filteredProjects, project)
				}
			}
		} else {
			filteredProjects = projects
		}

		// Order projects by project groups, then by project ID alphabetically
		sort.Slice(filteredProjects, func(i, j int) bool {
			groupI := filteredProjects[i].GetGroup()
			groupJ := filteredProjects[j].GetGroup()
			if groupI == groupJ {
				return filteredProjects[i].ProjectID < filteredProjects[j].ProjectID
			}
			// Find the index of the group in projectGroups to maintain the order
			indexI, indexJ := -1, -1
			for idx, group := range projectGroups {
				if group == groupI {
					indexI = idx
				}
				if group == groupJ {
					indexJ = idx
				}
			}
			// If both groups are found in projectGroups, use their order
			if indexI != -1 && indexJ != -1 {
				return indexI < indexJ
			}
			// If only one is found, it comes first
			if indexI != -1 {
				return true
			}
			if indexJ != -1 {
				return false
			}
			// If neither is found, sort alphabetically by group name
			return groupI < groupJ
		})

		// Extract project IDs
		projectIDs := make([]string, len(filteredProjects))
		for i, project := range filteredProjects {
			projectIDs[i] = project.ProjectID
		}

		// Split into batches based on batchLimit
		if len(projectIDs) > batchLimit {
			for i := 0; i < len(projectIDs); i += batchLimit {
				end := min(i+batchLimit, len(projectIDs))
				batchProjectIDs := projectIDs[i:end]
				batch := this.NewAutomationBatch(schedule, batchProjectIDs)
				schedule.Batches = append(schedule.Batches, batch)
			}
		} else {
			// Create a single batch with all projects
			batch := this.NewAutomationBatch(schedule, projectIDs)
			schedule.Batches = append(schedule.Batches, batch)
		}
		return schedule
	}

	// For batchLimit = 0, create batches by group
	// Group projects by their group
	groupProjects := make(map[string][]*Project)
	for _, project := range projects {
		group := project.GetGroup()
		if slices.Contains(projectGroups, group) {
			groupProjects[group] = append(groupProjects[group], project)
		}
	}

	// Create a batch for each group, preserving the order from projectGroups
	for _, group := range projectGroups {
		groupProjectList, ok := groupProjects[group]
		if !ok {
			continue
		}
		// Sort projects within each group by ProjectID
		sort.Slice(groupProjectList, func(i, j int) bool {
			return groupProjectList[i].ProjectID < groupProjectList[j].ProjectID
		})

		// Extract project IDs for this group
		groupProjectIDs := make([]string, len(groupProjectList))
		for i, project := range groupProjectList {
			groupProjectIDs[i] = project.ProjectID
		}

		batch := this.NewAutomationBatch(schedule, groupProjectIDs)
		schedule.Batches = append(schedule.Batches, batch)
	}

	return schedule
}

func (this *CopierController) NewAutomationBatch(schedule *AutomationSchedule, projectIDs []string) *AutomationBatch {
	// wait 0.7-1.3 times the wait seconds
	randomWaitMinutes := 0
	if this.options.AutomationBatchWait > 0 {
		randomWaitMinutes = rand.Intn(int(float64(this.options.AutomationBatchWait)*0.6)) + int(float64(this.options.AutomationBatchWait)*0.7)
	}
	batch := &AutomationBatch{
		RefID:       utils.NewRandomID(),
		Items:       AutomationItems{},
		StartTime:   nil,
		EndTime:     nil,
		WaitMinutes: randomWaitMinutes,
		ErrorEvents: []*AutomationErrorEvent{},
	}
	for _, projectID := range projectIDs {
		item, err := this.NewAutomationItem(schedule, projectID)
		if err != nil {
			logger.Errorf("new automation item failed, skip, projectID: %s, err: %v", projectID, err)
			continue
		}
		batch.Items = append(batch.Items, item)
	}
	return batch
}

func (this *CopierController) InitAutomationSchedule() *AutomationSchedule {
	this.scheduleMutex.Lock()
	defer this.scheduleMutex.Unlock()

	this.currentSchedule = this.automationSchedules.GetActiveSchedule()
	if this.currentSchedule == nil {
		this.currentSchedule = this.NewAutomationSchedule(ScheduleJobDefault)
		this.automationSchedules = append(this.automationSchedules, this.currentSchedule)
	}

	this.Storage.Save()
	return this.currentSchedule
}

// run a schedule, there is only one schedule can be running at a time
// because of the resource is limited on the pchrome instance, we can't be running multiple schedules at the same time
func (this *CopierController) RunAutomationSchedule(schedule *AutomationSchedule) {
	if schedule == nil {
		logger.Errorf("schedule is nil, skip")
		return
	}
	this.runningScheduleMutex.Lock()
	defer this.runningScheduleMutex.Unlock()

	switch schedule.Job {
	case ScheduleJobDefault:
		this.runDefaultSchedule(schedule)
	case ScheduleJobUpgradeExtension:
		this.runUpgradeExtensionSchedule(schedule)
	case ScheduleJobUpdateStats:
		this.runUpdateStatsSchedule(schedule)
	}
}

func (this *CopierController) runUpgradeExtensionSchedule(schedule *AutomationSchedule) {
	// upgrade extension comprise with two operations: a. click extension icon to open the extension page; b. refresh the page to force reload of the extension content.js
	// core logic to complete the upgrade extension:
	// 1. close all existing chromes
	// 2. prepare extensions for each project
	// 3. reload the page for each project
	// 4. wait for reload to compelete, and wait for agent to sync data
	// 5. then check if the agent update time is update to date, if not, wait for the next tick
	// 6. maxing total 10 minutes(batch timeout) for each batch to upgrade extension
	// 7. if all the agent is up to date, then close the extension page and start a new batch
	// 8. if current batch is hitting the timeout, then stop the batch and start a new batch
	// how to check if the agent is up to date?
	// 1. check the agent update time, if the agent is updated recently(within 10 minutes), then the agent is up to date
	if schedule.Job != ScheduleJobUpgradeExtension {
		logger.Errorf("schedule job is not upgrade extension, skip")
		return
	}
	scheduleMutex := getScheduleMutex(schedule.RefID)
	locked := scheduleMutex.TryLock()
	if !locked {
		logger.Errorf("schedule is currently running")
		return
	}
	defer scheduleMutex.Unlock()

	if schedule.StartTime == nil {
		schedule.StartTime = utils.Now()
	}
	// ensure chromes are closed before running
	projectIDs := []string{}
	projects := this.GetProjects(true)
	for _, project := range projects {
		projectIDs = append(projectIDs, project.ProjectID)
	}
	this.closeChromeForProjects(projectIDs)

	for i, batch := range schedule.Batches {
		// skip ended batches
		if batch.EndTime != nil {
			logger.Infof("skip ended batch: %s", batch.RefID)
			continue
		}
		if len(batch.Items) == 0 {
			batch.StartTime = utils.Now()
			batch.EndTime = batch.StartTime
			logger.Infof("skip batch with no items: %s", batch.RefID)
			continue
		}
		// skip canceled schedule
		if schedule.CancelTime != nil {
			logger.Infof("skip canceled schedule: %s", schedule.RefID)
			return
		}
		// skip paused schedule
		if schedule.PauseTime != nil {
			logger.Infof("skip paused schedule: %s", schedule.RefID)
			return
		}
		// do current batch
		schedule.CurrentBatch = batch

		logger.Infof("start upgrade extension batch: %s", batch.RefID)
		batch.StartTime = utils.Now()
		batch.EndTime = nil
		batch.ErrorEvents = []*AutomationErrorEvent{}

		this.startChromesForProjectIDs(batch.Items.ProjectIDs())
		this.prepareExtensions(batch.Items.ProjectIDs())

		interval := 10 * time.Second
		ticker := time.NewTicker(interval)
		defer ticker.Stop()
		counter := 1
		for range ticker.C {
			if schedule.CancelTime != nil {
				logger.Warnf("schedule is canceled, break batch running loop, return")
				return
			}
			if schedule.PauseTime != nil {
				logger.Infof("skip paused schedule: %s", schedule.RefID)
				return
			}
			batch.RunningTime += interval
			// check if all agents are up to date
			notUpdatedAgents := this.checkAgentForProjects(batch.Items.ProjectIDs(), 3*time.Minute)
			notUpdatedProjectIDs := make(map[string]bool)
			for _, agent := range notUpdatedAgents {
				notUpdatedProjectIDs[agent.ProjectID] = true
			}
			// Update item statuses
			allItemsSucceeded := true
			for _, item := range batch.Items {
				if item.Status != AutomationStatusSuccess {
					if _, found := notUpdatedProjectIDs[item.ProjectID]; !found {
						item.Status = AutomationStatusSuccess
						item.UpdateTime = time.Now()
					} else {
						allItemsSucceeded = false
					}
				}
			}

			if allItemsSucceeded {
				logger.Infof("all agents are up to date for batch: %s", batch.RefID)
				batch.EndTime = utils.Now()
				this.closeChromeForProjects(batch.Items.ProjectIDs())
				schedule.CurrentBatch = nil
				break
			}
			// if the batch is running for more than 10 minutes, stop it
			if batch.RunningTime > time.Duration(2*len(batch.Items))*time.Minute {
				logger.Warnf("batch timeout for upgrade extension: %s", batch.RefID)
				for _, item := range batch.Items {
					if item.Status != AutomationStatusSuccess {
						item.Status = AutomationStatusTimeout
						item.Comment = "batch timeout"
						item.UpdateTime = time.Now()
					}
				}
				batch.EndTime = utils.Now()
				this.closeChromeForProjects(batch.Items.ProjectIDs())
				schedule.CurrentBatch = nil
				break
			}
			counter++
			// save every 10 minutes
			if counter%(600) == 0 {
				this.Storage.Save()
			}
		}
		// wait for the next batch
		if i < len(schedule.Batches)-1 {
			nextBatchWait := schedule.Batches[i+1].WaitMinutes
			time.Sleep(time.Duration(nextBatchWait) * time.Minute)
		}
	}
	logger.Infof("upgrade extension all batches ended")
	schedule.CurrentBatch = nil
	schedule.UpdateTime = time.Now()
	schedule.EndTime = &schedule.UpdateTime
	this.Storage.Save()
}

func (this *CopierController) runUpdateStatsSchedule(schedule *AutomationSchedule) {
	// update stats comprise with two operations: a. click extension icon to open the extension page; b. add a ServerRequest for each project to update stats
	// core logic to complete the update stats:
	// 1. close all existing chromes
	// 2. prepare extensions for each project
	// 3. reload the page for each project
	// 4. wait for reload to compelete
	// 5. add a UpdateStats ServerRequest for each project
	// 6. maxing total 15 minutes(batch timeout) for each batch to update stats
	// 7. if current batch is hitting the timeout, then stop the batch and start a new batch
	// 8. if all the ServerRequest are finished, then close the extension page and start a new batch
	// 9. if current batch is hitting the timeout, then stop the batch and start a new batch
	// how to check if the ServerRequest are finished?
	// 1. check the stats on each agent, if the stats is updated recently(within 10 minutes), then the ServerRequest is finished
	if schedule.Job != ScheduleJobUpdateStats {
		logger.Errorf("schedule job is not update status, skip")
		return
	}
	scheduleMutex := getScheduleMutex(schedule.RefID)
	locked := scheduleMutex.TryLock()
	if !locked {
		logger.Errorf("schedule is currently running")
		return
	}
	defer scheduleMutex.Unlock()

	if schedule.StartTime == nil {
		schedule.StartTime = utils.Now()
	}
	// ensure chromes are closed before running
	projectIDs := []string{}
	projects := this.GetProjects(true)
	for _, project := range projects {
		projectIDs = append(projectIDs, project.ProjectID)
	}
	this.closeChromeForProjects(projectIDs)

	for i, batch := range schedule.Batches {
		// skip ended batches
		if batch.EndTime != nil {
			logger.Infof("skip ended batch: %s", batch.RefID)
			continue
		}
		if len(batch.Items) == 0 {
			batch.StartTime = utils.Now()
			batch.EndTime = batch.StartTime
			logger.Infof("skip batch with no items: %s", batch.RefID)
			continue
		}
		// skip canceled schedule
		if schedule.CancelTime != nil {
			logger.Infof("skip canceled schedule: %s", schedule.RefID)
			return
		}
		// skip paused schedule
		if schedule.PauseTime != nil {
			logger.Infof("skip paused schedule: %s", schedule.RefID)
			return
		}
		// do current batch
		schedule.CurrentBatch = batch

		logger.Infof("start update stats batch: %s", batch.RefID)
		batch.StartTime = utils.Now()
		batch.EndTime = nil
		batch.ErrorEvents = []*AutomationErrorEvent{}

		this.startChromesForProjectIDs(batch.Items.ProjectIDs())
		this.prepareExtensions(batch.Items.ProjectIDs())

		time.Sleep(5 * time.Second)
		// add server requests
		for _, item := range batch.Items {
			project, ok := this.Projects.Load(item.ProjectID)
			if !ok {
				continue
			}
			for _, agent := range project.Agents {
				this.AddServerRequest(agent.ProjectID, agent.UserID, ServerRequestTypeUpdateStats, 0)
			}
		}

		interval := 30 * time.Second
		ticker := time.NewTicker(interval)
		defer ticker.Stop()
		counter := 1
		for range ticker.C {
			if schedule.CancelTime != nil {
				logger.Warnf("schedule is canceled, break batch running loop, return")
				return
			}
			if schedule.PauseTime != nil {
				logger.Infof("skip paused schedule: %s", schedule.RefID)
				return
			}
			batch.RunningTime += interval
			// check if all server requests are finished
			notUpdatedAgents := this.checkAgentStatsUpdate(batch.Items.ProjectIDs(), 15*time.Minute)
			logger.Infof("process update stats schedule, check agent stats update, not updated agents: %d, schedule: %s, batch: %s", len(notUpdatedAgents), schedule.RefID, batch.RefID)
			notUpdatedProjectIDs := make(map[string]bool)
			for _, agent := range notUpdatedAgents {
				notUpdatedProjectIDs[agent.ProjectID] = true
			}

			// retry add server requests if failed
			for _, agent := range notUpdatedAgents {
				this.AddServerRequest(agent.ProjectID, agent.UserID, ServerRequestTypeUpdateStats, 0)
				logger.Infof("process update stats schedule, retry add server request for agent: %s, project: %s, schedule: %s, batch: %s", agent.UserID, agent.ProjectID, schedule.RefID, batch.RefID)
			}

			// Update item statuses
			allItemsSucceeded := true
			for _, item := range batch.Items {
				if item.Status != AutomationStatusSuccess {
					if _, found := notUpdatedProjectIDs[item.ProjectID]; !found {
						item.Status = AutomationStatusSuccess
						item.UpdateTime = time.Now()
					} else {
						allItemsSucceeded = false
					}
				}
			}

			if allItemsSucceeded {
				logger.Infof("all server requests are finished for batch: %s", batch.RefID)
				batch.EndTime = utils.Now()
				schedule.CurrentBatch = nil
				break
			}
			// if the batch is running for more than 15 minutes, stop it
			if batch.RunningTime > time.Duration(3*len(batch.Items))*time.Minute {
				logger.Warnf("batch timeout for update stats: %s", batch.RefID)
				for _, item := range batch.Items {
					if item.Status != AutomationStatusSuccess {
						item.Status = AutomationStatusTimeout
						item.Comment = "batch timeout"
						item.UpdateTime = time.Now()
					}
				}
				batch.EndTime = utils.Now()
				schedule.CurrentBatch = nil
				break
			}
			counter++
			// save every 10 minutes
			this.Storage.Save()
		}
		this.closeChromeForProjects(batch.Items.ProjectIDs())
		// wait for the next batch
		if i < len(schedule.Batches)-1 {
			nextBatchWait := schedule.Batches[i+1].WaitMinutes
			time.Sleep(time.Duration(nextBatchWait) * time.Minute)
		}
	}
	logger.Infof("update stats all batches ended")
	schedule.CurrentBatch = nil
	schedule.UpdateTime = time.Now()
	schedule.EndTime = &schedule.UpdateTime
	this.Storage.Save()
}

func (this *CopierController) checkAgentStatsUpdate(projectIDs []string, threshold time.Duration) (failedAgents []*Agent) {
	for _, projectID := range projectIDs {
		project, ok := this.Projects.Load(projectID)
		if !ok {
			continue
		}
		for _, agent := range project.Agents {
			if agent.Stats == nil || time.Since(agent.Stats.CreateTime) > threshold {
				failedAgents = append(failedAgents, agent)
			}
		}
	}
	return
}

// default schedule, run every day
// core logic:
// 1. start a new batch
// 2. prepare extensions for each project
// 3. resume projects to run
// 4. check if the batch is ended
// 5. if the batch is ended, close chrome for all projects
// 6. if the batch is ended, start a new batch
// 7. if the batch is not ended, wait for the next tick
func (this *CopierController) runDefaultSchedule(schedule *AutomationSchedule) (err error) {
	if schedule.Job != ScheduleJobDefault {
		logger.Errorf("schedule job is not default, skip")
		return fmt.Errorf("schedule job is not default")
	}
	scheduleMutex := getScheduleMutex(schedule.RefID)
	locked := scheduleMutex.TryLock()
	if !locked {
		return fmt.Errorf("schedule is currently running")
	}
	defer scheduleMutex.Unlock()

	if schedule.StartTime == nil {
		schedule.StartTime = utils.Now()
	}
	// ensure projects are stopped and chromes are closed before running
	projectIDs := []string{}
	projects := this.GetProjects(true)
	for _, project := range projects {
		projectIDs = append(projectIDs, project.ProjectID)
	}
	this.closeChromeForProjects(projectIDs)

	for i, batch := range schedule.Batches {
		// skip ended batches
		if batch.EndTime != nil {
			logger.Infof("skip ended batch: %s", batch.RefID)
			continue
		}
		if len(batch.Items) == 0 {
			batch.StartTime = utils.Now()
			batch.EndTime = batch.StartTime
			logger.Infof("skip batch with no items: %s", batch.RefID)
			continue
		}
		// skip canceled schedule
		// set schedule.currentBatch == nil when canceling schedule
		if schedule.CancelTime != nil {
			logger.Infof("skip canceled schedule: %s", schedule.RefID)
			return fmt.Errorf("schedule is canceled")
		}
		// skip paused schedule
		if schedule.PauseTime != nil {
			logger.Infof("skip paused schedule: %s", schedule.RefID)
			return fmt.Errorf("schedule is paused")
		}
		// do current batch
		schedule.CurrentBatch = batch

		logger.Infof("start automation batch: %s", batch.RefID)
		this.StartAutomationBatch(batch)

		interval := 30 * time.Second
		ticker := time.NewTicker(interval)
		batchTimedOut := false
		defer ticker.Stop()
		counter := 1
		for range ticker.C {
			batch.RunningTime += interval
			// cancel-current-schedule will set cancelTime, and stop all items and close chromes in current batch
			// no need to proceed remaining procedures
			if schedule.CancelTime != nil {
				logger.Warnf("schedule is canceled, break batch running loop, return")
				return fmt.Errorf("schedule is canceled")
			}
			// skip paused schedule
			if schedule.PauseTime != nil {
				logger.Infof("skip paused schedule: %s", schedule.RefID)
				return fmt.Errorf("schedule is paused")
			}

			this.options.ActivateChrome = true // ensure activateChrome always true
			ended := this.checkAutomationBatchEnded(batch, false)
			// every 10 minutes, retry prepare failed items, the page should be reloaded by the extension
			if !ended && counter%(20) == 0 {
				logger.Infof("retry prepare failed items for automation batch: %s", batch.RefID)
				ended = this.checkAutomationBatchEnded(batch, true)
			}
			logger.Infof("checking automation batch ended: %s, ended: %v", batch.RefID, ended)
			if ended {
				logger.Infof("automation batch ended: %s", batch.RefID)
				time.Sleep(5 * time.Second)
				this.closeChromeForProjects(batch.Items.ProjectIDs())
				schedule.CurrentBatch = nil
				break
			}
			// if the batch is running for more than 5 hours, stop it
			if this.options.AutomationBatchTimeout > 0 {
				if batch.RunningTime > time.Duration(this.options.AutomationBatchTimeout*60)*time.Minute {
					logger.Warnf("batch timeout for default schedule: %s, running time: %s", batch.RefID, batch.RunningTime)
					batchTimedOut = true
					for _, item := range batch.Items {
						if item.Status != AutomationStatusSuccess {
							project, ok := this.Projects.Load(item.ProjectID)
							if ok && project.IsSuccessfullyFinished() {
								item.Status = AutomationStatusSuccess
								item.Comment = "successfully finished"
								item.UpdateTime = time.Now()
							} else {
								item.Status = AutomationStatusTimeout
								item.Comment = "batch timeout"
								item.UpdateTime = time.Now()
							}
						}
					}
					batch.EndTime = utils.Now()
					schedule.CurrentBatch = nil
					break
				}
			}
			counter++
			this.Storage.Save()
		}
		if batchTimedOut {
			this.stopProjectsAndCloseChromes(batch.Items.ProjectIDs(), ProjectStatusCommentAutomationTimeout)
		}
		// wait for the next batch
		if i < len(schedule.Batches)-1 {
			nextBatchWait := schedule.Batches[i+1].WaitMinutes
			time.Sleep(time.Duration(nextBatchWait) * time.Minute)
		}
	}
	logger.Infof("automation all batches ended")
	schedule.CurrentBatch = nil
	schedule.UpdateTime = time.Now()
	schedule.EndTime = &schedule.UpdateTime
	this.Storage.Save()
	return nil
}

// findScheduleByRefID finds a schedule by its refID.
func (this *CopierController) findScheduleByRefID(refID string) *AutomationSchedule {
	for _, schedule := range this.automationSchedules {
		if schedule.RefID == refID {
			return schedule
		}
	}
	return nil
}

// CancelSchedule cancels a schedule by its refID.
func (this *CopierController) CancelSchedule(refID string) error {
	this.scheduleMutex.Lock()
	defer this.scheduleMutex.Unlock()

	schedule := this.findScheduleByRefID(refID)
	if schedule == nil {
		return fmt.Errorf("schedule with refID %s not found", refID)
	}

	if this.currentSchedule != nil && this.currentSchedule.RefID == schedule.RefID {
		this.options.EnableAutomation = false
		this.currentSchedule = nil
	}

	schedule.CancelTime = utils.Now()
	schedule.EndTime = schedule.CancelTime
	schedule.UpdateTime = *schedule.CancelTime
	if schedule.CurrentBatch != nil {
		this.stopProjectsAndCloseChromes(schedule.CurrentBatch.Items.ProjectIDs(), ProjectStatusCommentAutomationCanceled)
		schedule.CurrentBatch = nil
	}
	this.Storage.Save()
	ticker := time.NewTicker(1 * time.Second)
	counter := 0
	defer ticker.Stop()
	for range ticker.C {
		scheduleMutex := getScheduleMutex(schedule.RefID)
		locked := scheduleMutex.TryLock()
		if locked {
			scheduleMutex.Unlock()
			break
		}
		counter++
		if counter > 30 {
			logger.Warnf("cancel schedule, but the current batch is not ended, break")
			return fmt.Errorf("cancel schedule, wait for the current batch to be ended timeout")
		}
	}
	return nil
}

// PauseSchedule pauses a schedule by its refID.
func (this *CopierController) PauseSchedule(refID string) error {
	this.scheduleMutex.Lock()
	defer this.scheduleMutex.Unlock()

	schedule := this.findScheduleByRefID(refID)
	if schedule == nil {
		return fmt.Errorf("schedule with refID %s not found", refID)
	}

	if schedule.CancelTime != nil {
		return fmt.Errorf("schedule is canceled")
	}
	schedule.PauseTime = utils.Now()
	schedule.UpdateTime = *schedule.PauseTime
	if schedule.CurrentBatch != nil {
		this.stopProjectsAndCloseChromes(schedule.CurrentBatch.Items.ProjectIDs(), ProjectStatusCommentAutomationPaused)
		schedule.CurrentBatch = nil
	}
	this.Storage.Save()
	ticker := time.NewTicker(1 * time.Second)
	counter := 0
	defer ticker.Stop()
	for range ticker.C {
		scheduleMutex := getScheduleMutex(schedule.RefID)
		locked := scheduleMutex.TryLock()
		if locked {
			scheduleMutex.Unlock()
			break
		}
		counter++
		if counter > 30 {
			logger.Warnf("pause schedule, but the current batch is not ended, break")
			return fmt.Errorf("pause schedule, wait for the current batch to be ended timeout")
		}
	}
	return nil
}

// ResumeSchedule resumes a schedule by its refID.
func (this *CopierController) ResumeSchedule(refID string) error {
	this.scheduleMutex.Lock()
	defer this.scheduleMutex.Unlock()

	schedule := this.findScheduleByRefID(refID)
	if schedule == nil {
		return fmt.Errorf("schedule with refID %s not found", refID)
	}

	if schedule.CancelTime != nil {
		return fmt.Errorf("schedule is canceled")
	}
	schedule.PauseTime = nil
	schedule.UpdateTime = time.Now()
	go this.RunAutomationSchedule(schedule)
	this.Storage.Save()
	return nil
}

func (this *CopierController) stopProjectsAndCloseChromes(projectIDs []string, comment ProjectStatusComment) {
	logger.Infof("stopProjectsAndCloseChromes started for %d projects with comment: %s", len(projectIDs), comment)
	for _, projectID := range projectIDs {
		logger.Infof("Stopping project %s", projectID)
		this.StopProject(projectID, comment)
		logger.Infof("Finished stopping project %s", projectID)
	}
	logger.Infof("Finished stopping all projects, closing chromes")
	this.closeChromeForProjects(projectIDs)
	logger.Infof("stopProjectsAndCloseChromes finished for %d projects", len(projectIDs))
}

func (this *AutomationItems) ProjectIDs() []string {
	projectIDs := []string{}
	for _, item := range *this {
		projectIDs = append(projectIDs, item.ProjectID)
	}
	return projectIDs
}

func (this *CopierController) checkAutomationBatchEnded(batch *AutomationBatch, retryPrepare bool) (ended bool) {
	_runningStartTime := time.Now()
	defer func() {
		batch.RunningTime += time.Since(_runningStartTime)
	}()

	origProjectIDs := batch.Items.ProjectIDs()
	notRunnableProjectIDs := []string{}
	runnableStoppedItems := AutomationItems{}
	for _, item := range batch.Items {
		project, ok := this.Projects.Load(item.ProjectID)
		if !ok {
			notRunnableProjectIDs = append(notRunnableProjectIDs, item.ProjectID)
			continue
		}

		// Update item's performance snapshot
		if project.TodayPerformance != nil {
			dailyPerformance := &Performance{}
			copier.Copy(dailyPerformance, project.TodayPerformance)
			dailyPerformance.AgentTrades = nil
			item.DailyPerformance = dailyPerformance
		}

		// if project is not runable and safely stopped, set status to success
		if this.IsProjectRunnable(project) {
			if project.Status == ProjectStatusStopped {
				runnableStoppedItems = append(runnableStoppedItems, item)
			}
		} else {
			// 只有安全终止的项目才可以放到 notRunnableProjectIDs 中，确保其状态不是 running 或者刚 stopped 的
			// 否则可能会出现上一个 batch 报告 ended 但是 closeChrome 没有将其关闭的问题
			if !project.IsSafelyStopped() {
				continue
			}
			if project.IsSuccessfullyFinished() {
				item.Status = AutomationStatusSuccess
				item.Comment = ""
				item.UpdateTime = time.Now()
			}
			notRunnableProjectIDs = append(notRunnableProjectIDs, item.ProjectID)
			continue
		}
	}

	logger.Infof("not runnable project ids: %v, orig project ids: %v", notRunnableProjectIDs, origProjectIDs)
	// if all projects are not runnable, set ended to true
	if len(notRunnableProjectIDs) == len(origProjectIDs) {
		batch.EndTime = utils.Now()
		ended = true
	}

	// try to make runnable project run again
	if retryPrepare {
		this.startAutomationItems(batch, runnableStoppedItems)
	}
	return
}

func (this *CopierController) StartAutomationBatch(batch *AutomationBatch) {
	_runningStartTime := time.Now()
	defer func() {
		batch.RunningTime += time.Since(_runningStartTime)
	}()

	// prepare items
	projectIDs := batch.Items.ProjectIDs()
	if len(projectIDs) == 0 {
		return
	}

	if batch.StartTime == nil {
		batch.StartTime = utils.Now()
	}
	batch.EndTime = nil
	batch.ErrorEvents = []*AutomationErrorEvent{}

	this.startAutomationItems(batch, batch.Items)
}

func (this *CopierController) startAutomationItems(batch *AutomationBatch, items AutomationItems) {
	projectIDs := items.ProjectIDs()
	// temporarily disable activate chrome
	this.options.ActivateChrome = false
	defer func() {
		this.options.ActivateChrome = true
	}()

	// prepare extensions, retry 3 times
	retryLimit := 3
	notPreparedAgents := []*Agent{}
	for i := 0; i < retryLimit; i++ {
		if i > 0 {
			logger.Infof("retrying prepare agent #%d", i)
		}
		this.startChromesForProjectIDs(projectIDs)
		this.prepareExtensions(projectIDs) // force activate chrome during prepare extensions, options.ActivateChrome is false
		notPreparedAgents = this.checkAgentForProjects(projectIDs, 3*time.Minute)
		if len(notPreparedAgents) == 0 {
			break
		}

		notPreparedProjectIDs := []string{}
		for _, agent := range notPreparedAgents {
			notPreparedProjectIDs = append(notPreparedProjectIDs, agent.ProjectID)
			batch.ErrorEvents = append(batch.ErrorEvents, &AutomationErrorEvent{
				ProjectID: agent.ProjectID,
				UserID:    agent.UserID,
				Type:      AutomationErrorEventTypePrepareFailed,
				Time:      time.Now(),
			})
		}
		projectIDs = utils.Unique(notPreparedProjectIDs) // retry with failed projects only
		time.Sleep(1000 * time.Millisecond)
	}
	// set items status
outerLoop:
	for _, item := range batch.Items {
		// skip success items, the func can be called when retry prepare the items
		if item.Status == AutomationStatusSuccess {
			continue
		}
		for _, agent := range notPreparedAgents {
			if item.ProjectID == agent.ProjectID {
				item.Status = AutomationStatusPrepareFailed
				item.Comment = fmt.Sprintf("prepare failed for userID: %s, last update time: %s", agent.UserID, agent.CreateTime)
				item.UpdateTime = time.Now()
				continue outerLoop
			}
		}
		// if there is no failed agent, set the item to prepared
		// override status if it was previously prepare failed, because the start batch can be retried
		item.Status = AutomationStatusPrepared
		item.Comment = ""
		item.UpdateTime = time.Now()
		this.runAutomationItem(item)
	}
	this.Storage.Save()
}

func (this *CopierController) runAutomationItem(item *AutomationItem) {
	if item.Status != AutomationStatusPrepared {
		logger.Errorf("automation item is not prepared: %v", item)
		return
	}
	project, ok := this.Projects.Load(item.ProjectID)
	if !ok {
		item.Status = AutomationStatusError
		item.Comment = "project not found"
		item.UpdateTime = time.Now()
		return
	}
	if !this.IsProjectRunnable(project) {
		if !project.IsSuccessfullyFinished() {
			item.Status = AutomationStatusError
			item.Comment = "project is not runnable"
			item.UpdateTime = time.Now()
		}
		return
	}
	logger.Infof("run automation item, resume project: %s", project.ProjectID)
	this.ResumeProject(project.ProjectID, ProjectStatusComment("automation"))
	item.Status = AutomationStatusRunning
	item.Comment = ""
	item.UpdateTime = time.Now()
}

// checkAgentForProjects checks if the agent is up to date for the projects
// if the agent is not up to date, it will be added to the failedAgents list
func (this *CopierController) checkAgentForProjects(projectIDs []string, threshold time.Duration) (failedAgents []*Agent) {
	for _, projectID := range projectIDs {
		project, ok := this.Projects.Load(projectID)
		if !ok {
			continue
		}
		for _, agent := range project.Agents {
			if time.Since(agent.CreateTime) > threshold {
				failedAgents = append(failedAgents, agent)
			}
		}
	}
	return
}

func (this *CopierController) prepareExtensions(projectIDs []string) {
	// for each agent:
	// 1. activate chrome for project
	// 2. click on the extension icon
	// 3. send server request to reload page
	logger.Infof("start automation for project: %s", projectIDs)
	for _, projectID := range projectIDs {
		project, ok := this.Projects.Load(projectID)
		if !ok {
			continue
		}
		for _, agent := range project.Agents {
			logger.Infof("start automation for project: %s, agent: %s", projectID, agent.UserID)
			err := this.activateChromeByAddress(projectID, agent.UserID, true)
			if err == nil {
				time.Sleep(1000 * time.Millisecond)
				err = this.ensureExtensionOpened()
				if err != nil {
					logger.Errorf("failed to ensure extension opened: %v", err)
				}
				time.Sleep(1000 * time.Millisecond)
				this.AddServerRequest(projectID, agent.UserID, ServerRequestTypeNavigateToTrade, 4)
				err = this.reloadChrome(projectID, agent.UserID, false)
				if err != nil {
					logger.Errorf("prepare extenstion, failed to reload chrome, project: %s, agent: %s, error: %v", projectID, agent.UserID, err)
				}
			} else {
				logger.Errorf("failed to activate chrome: %v", err)
			}
		}
	}
}
